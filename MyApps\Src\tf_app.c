#include "tf_app.h"

FIL fdst;
uint16_t i = 0, count, result = 0;
UINT br, bw;

sd_card_info_struct sd_cardinfo;

BYTE buffer[128];
BYTE filebuffer[128];

ErrStatus memory_compare(uint8_t* src, uint8_t* dst, uint16_t length) 
{
    while(length --){
        if(*src++ != *dst++)
            return ERROR;
    }
    return SUCCESS;
}

/**
 * @brief       通过串口打印SD卡相关信息
 * @param       无
 * @retval      无
 */
void card_info_get(void)
{
    sd_card_info_struct sd_cardinfo;      // SD卡信息结构体
    sd_error_enum status;                 // SD卡操作状态
    uint32_t block_count, block_size;
    
    // 获取SD卡信息
    status = sd_card_information_get(&sd_cardinfo);
    
    if(SD_OK == status)
    {
        Usart0Printf("\r\n*** SD Card Info ***\r\n");
        
        // 打印卡类型
        switch(sd_cardinfo.card_type)
        {
            case SDIO_STD_CAPACITY_SD_CARD_V1_1:
                Usart0Printf("Card Type: Standard Capacity SD Card V1.1\r\n");
                break;
            case SDIO_STD_CAPACITY_SD_CARD_V2_0:
                Usart0Printf("Card Type: Standard Capacity SD Card V2.0\r\n");
                break;
            case SDIO_HIGH_CAPACITY_SD_CARD:
                Usart0Printf("Card Type: High Capacity SD Card\r\n");
                break;
            case SDIO_MULTIMEDIA_CARD:
                Usart0Printf("Card Type: Multimedia Card\r\n");
                break;
            case SDIO_HIGH_CAPACITY_MULTIMEDIA_CARD:
                Usart0Printf("Card Type: High Capacity Multimedia Card\r\n");
                break;
            case SDIO_HIGH_SPEED_MULTIMEDIA_CARD:
                Usart0Printf("Card Type: High Speed Multimedia Card\r\n");
                break;
            default:
                Usart0Printf("Card Type: Unknown\r\n");
                break;
        }
        
        // 打印卡容量和块大小
        block_count = (sd_cardinfo.card_csd.c_size + 1) * 1024;
        block_size = 512;
        Usart0Printf("\r\n## Device size is %dKB (%.2fGB)##", sd_card_capacity_get(), sd_card_capacity_get() / 1024.0f / 1024.0f);
        Usart0Printf("\r\n## Block size is %dB ##", block_size);
        Usart0Printf("\r\n## Block count is %d ##", block_count);
        
        // 打印制造商ID和产品名称
        Usart0Printf("Manufacturer ID: 0x%X\r\n", sd_cardinfo.card_cid.mid);
        Usart0Printf("OEM/Application ID: 0x%X\r\n", sd_cardinfo.card_cid.oid);
        
        // 打印产品名称 (PNM)
        uint8_t pnm[6];
        pnm[0] = (sd_cardinfo.card_cid.pnm0 >> 24) & 0xFF;
        pnm[1] = (sd_cardinfo.card_cid.pnm0 >> 16) & 0xFF;
        pnm[2] = (sd_cardinfo.card_cid.pnm0 >> 8) & 0xFF;
        pnm[3] = sd_cardinfo.card_cid.pnm0 & 0xFF;
        pnm[4] = sd_cardinfo.card_cid.pnm1 & 0xFF;
        pnm[5] = '\0';
        Usart0Printf("Product Name: %s\r\n", pnm);
        
        // 打印产品版本和序列号
        Usart0Printf("Product Revision: %d.%d\r\n", (sd_cardinfo.card_cid.prv >> 4) & 0x0F, sd_cardinfo.card_cid.prv & 0x0F);
        // 序列号以无符号方式显示，避免负数
        Usart0Printf("Product Serial Number: 0x%08X\r\n", sd_cardinfo.card_cid.psn);
        
        // 打印CSD版本和其它CSD信息
        Usart0Printf("CSD Version: %d.0\r\n", sd_cardinfo.card_csd.csd_struct + 1);
        
    }
    else
    {
        Usart0Printf("\r\nFailed to get SD card information, error code: %d\r\n", status);
    }
}

void TfCardTest(void)
{
    uint16_t k = 5;
    DSTATUS stat = 0;
    do
    {
        stat = disk_initialize(0); 			//初始化SD卡（设备号0）,物理驱动器编号,每个物理驱动器（如硬盘、U 盘等）通常都被分配一个唯一的编号。
    }while((stat != 0) && (--k));			//如果初始化失败，重试最多k次。
    
    card_info_get();
    
    Usart0Printf("SD Card disk_initialize:%d\r\n",stat);
    f_mount(0, &fs);						 //挂载SD卡的文件系统（设备号0）。
    Usart0Printf("SD Card f_mount:%d\r\n",stat);
    
    if(RES_OK == stat)						 //返回挂载结果（FR_OK 表示成功）。
    {        
        Usart0Printf("\r\nSD Card Initialize Success!\r\n");
     
        result = f_open(&fdst, "0:/FATFS.TXT", FA_CREATE_ALWAYS | FA_WRITE);		//在SD卡上创建文件FATFS.TXT。
     
        sprintf((char *)filebuffer, "HELLO MCUSTUDIO");

        //result = f_write(&fdst, textfilebuffer, sizeof(textfilebuffer), &bw); 	//将textfilebuffer中的数据写入文件。
        result = f_write(&fdst, filebuffer, sizeof(filebuffer), &bw);				//将filebuffer中的数据写入文件。
        
        /**********检查写入结果 begin****************/
        if(FR_OK == result)		
            Usart0Printf("FATFS FILE write Success!\r\n");
        else
        {
            Usart0Printf("FATFS FILE write failed!\r\n");
        }
        /**********检查写入结果 end****************/
        
        f_close(&fdst);//关闭文件
        
        
        f_open(&fdst, "0:/FATFS.TXT", FA_OPEN_EXISTING | FA_READ);	//以只读方式重新打开文件
        br = 1;
        
        /**********循环读取文件内容 begin****************/
        for(;;)
        {
            // 清空缓冲区
            for (count=0; count<128; count++)
            {
                buffer[count]=0;
            }
            // 读取文件内容到buffer
            result = f_read(&fdst, buffer, sizeof(buffer), &br);
            if ((0 == result)|| (0 == br))
            {
                break;
            }
        }
        /**********循环读取文件内容 end****************/
        
        // 比较读取的内容与写入的内容是否一致
        if(SUCCESS == memory_compare(buffer, filebuffer, 128))
        {
            Usart0Printf("FATFS Read File Success!\r\nThe content is:%s\r\n",buffer);
        }
        else
        {
            Usart0Printf("FATFS FILE read failed!\n");            
        }
         f_close(&fdst);//关闭文件
    }
}
