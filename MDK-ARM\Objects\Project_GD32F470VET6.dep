Dependencies for Project 'Project', Target 'GD32F470VET6': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCOMPLIER506
F (..\Core\Src\gd32f4xx_it.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (..\Core\Inc\gd32f4xx_it.h)(0x688E2E55)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\Core\Src\gd32f470vet6_bsp.c)(0x688E35B4)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f470vet6_bsp.o --omf_browse .\objects\gd32f470vet6_bsp.crf --depend .\objects\gd32f470vet6_bsp.d)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\Core\Src\main.c)(0x688E40D9)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\Core\Src\scheduler.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\scheduler.o --omf_browse .\objects\scheduler.crf --depend .\objects\scheduler.d)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\Core\Src\systick.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
F (..\MyApps\Src\adc_app.c)(0x688E2E56)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\adc_app.o --omf_browse .\objects\adc_app.crf --depend .\objects\adc_app.d)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\MyApps\Src\led_app.c)(0x688E35E0)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\led_app.o --omf_browse .\objects\led_app.crf --depend .\objects\led_app.d)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\MyApps\Src\btn_app.c)(0x688E2E56)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\btn_app.o --omf_browse .\objects\btn_app.crf --depend .\objects\btn_app.d)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\MyApps\Src\oled_app.c)(0x688E2E56)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\oled_app.o --omf_browse .\objects\oled_app.crf --depend .\objects\oled_app.d)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\MyApps\Src\rtc_app.c)(0x688E2E56)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\rtc_app.o --omf_browse .\objects\rtc_app.crf --depend .\objects\rtc_app.d)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
F (..\MyApps\Src\tf_app.c)(0x688E2E56)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\tf_app.o --omf_browse .\objects\tf_app.crf --depend .\objects\tf_app.d)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\MyApps\Src\usart_app.c)(0x688E2E56)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\usart_app.o --omf_browse .\objects\usart_app.crf --depend .\objects\usart_app.d)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\Compenents\Btn\Src\ebtn.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\ebtn.o --omf_browse .\objects\ebtn.crf --depend .\objects\ebtn.d)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
F (..\Compenents\Oled\Src\oled.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\oled.o --omf_browse .\objects\oled.crf --depend .\objects\oled.d)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Compenents\Oled\Inc\oledfont.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
F (..\Compenents\Gd25qxx\Src\gd25qxx.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd25qxx.o --omf_browse .\objects\gd25qxx.crf --depend .\objects\gd25qxx.d)
I (..\Core\Inc\gd32f470vet6_bsp.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\string.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\math.h)(0x688602D2)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Core\Inc\systick.h)(0x688E2E55)
I (..\Core\Inc\scheduler.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\ebtn.h)(0x688E2E55)
I (..\Compenents\Btn\Inc\bit_array.h)(0x688E2E55)
I (..\Compenents\Oled\Inc\oled.h)(0x688E2E55)
I (..\Compenents\Gd25qxx\Inc\gd25qxx.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\MyApps\Inc\led_app.h)(0x688E2E56)
I (..\MyApps\Inc\btn_app.h)(0x688E2E56)
I (..\MyApps\Inc\adc_app.h)(0x688E2E56)
I (..\MyApps\Inc\usart_app.h)(0x688E2E56)
I (..\MyApps\Inc\oled_app.h)(0x688E2E56)
I (..\MyApps\Inc\tf_app.h)(0x688E2E56)
I (..\MyApps\Inc\rtc_app.h)(0x688E2E56)
F (..\Compenents\Sdio\Src\sdio_sdcard.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\sdio_sdcard.o --omf_browse .\objects\sdio_sdcard.crf --depend .\objects\sdio_sdcard.d)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stddef.h)(0x5E8E3CC2)
F (..\Compenents\Fatfs\Src\diskio.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\diskio.o --omf_browse .\objects\diskio.crf --depend .\objects\diskio.d)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Sdio\Inc\sdio_sdcard.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdio.h)(0x5E8E3CC2)
F (..\Compenents\Fatfs\Src\ff.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\ff.o --omf_browse .\objects\ff.crf --depend .\objects\ff.d)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\diskio.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdarg.h)(0x5E8E3CC2)
F (..\Compenents\Fatfs\Src\unicode.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\unicode.o --omf_browse .\objects\unicode.crf --depend .\objects\unicode.d)
I (..\Compenents\Fatfs\Inc\ff.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\integer.h)(0x688E2E55)
I (..\Compenents\Fatfs\Inc\ffconf.h)(0x688E2E55)
F (..\Firmware\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x688E2E55)(--cpu Cortex-M4.fp.sp -g --apcs=interwork 

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 541" --pd "GD32F470 SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
F (..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x688E2E55)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -g -O0 --apcs=interwork --split_sections -I ..\Core\Inc -I ..\MyApps\Inc -I ..\Compenents\Btn\Inc -I ..\Compenents\Oled\Inc -I ..\Compenents\Sdio\Inc -I ..\Compenents\Fatfs\Inc -I ..\Firmware\CMSIS -I ..\Firmware\CMSIS\GD\GD32F4xx\Include -I ..\Firmware\GD32F4xx_standard_peripheral\Include -I ..\Compenents\Gd25qxx\Inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="541" -DGD32F470

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdint.h)(0x5E8E3CC2)
I (..\Firmware\CMSIS\core_cmInstr.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cmFunc.h)(0x688E2E55)
I (..\Firmware\CMSIS\core_cm4_simd.h)(0x688E2E55)
I (..\Firmware\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x688E2E55)
I (..\Core\Inc\gd32f4xx_libopt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x688E2E55)
I (C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCOMPLIER506\include\stdlib.h)(0x5E8E3CC2)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x688E2E55)
I (..\Firmware\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x688E2E55)
