#include "gd32f470vet6_bsp.h"

// RS485方向控制引脚定义
#define RS485_DIR_PORT          GPIOA
#define RS485_DIR_PIN           GPIO_PIN_1
#define RS485_DIR_RCU_PERIPH    RCU_GPIOA

// RS485发送使能
void rs485_send_enable(void) {
    gpio_bit_set(RS485_DIR_PORT, RS485_DIR_PIN); // 将PA1置高，使能MAX3485ESA发送
    // 根据MAX3485ESA数据手册，Driver Enable Setup Time通常为几十纳秒
    // 但为确保在各种条件下的稳定性，给予足够的建立时间
    delay_1ms(2); // 增加延时确保MAX3485ESA完全切换到发送模式
}

// RS485接收使能
void rs485_receive_enable(void) {
   gpio_bit_reset(RS485_DIR_PORT, RS485_DIR_PIN); // 将PA1置低，使能MAX3485ESA接收
    // 根据MAX3485ESA数据手册，可能需要一个短暂的保持延时 (Receiver Enable Hold Time)
    // delay_us(10);
    delay_1ms(1);
}

// USART1 初始化
void usart1_rs485_init(uint32_t baudval) {
    /* 使能GPIOA和USART1时钟 */
    rcu_periph_clock_enable(RCU_GPIOA);
    rcu_periph_clock_enable(RCU_USART1);

    /* 配置RS485方向控制引脚 (PA1) 为推挽输出 */
    gpio_mode_set(RS485_DIR_PORT, GPIO_MODE_OUTPUT, GPIO_PUPD_NONE, RS485_DIR_PIN);
    gpio_output_options_set(RS485_DIR_PORT, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, RS485_DIR_PIN);

    /* 配置USART1 TX (PA2) 为复用推挽输出 */
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_2); // TX
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_2);
    gpio_af_set(GPIOA, GPIO_AF_2, GPIO_PIN_2); // PA2的AF2是USART1_TX

    /* 配置USART1 RX (PA3) 为浮空输入 */
    gpio_mode_set(GPIOA, GPIO_MODE_AF, GPIO_PUPD_PULLUP, GPIO_PIN_3); // RX
    gpio_output_options_set(GPIOA, GPIO_OTYPE_PP, GPIO_OSPEED_50MHZ, GPIO_PIN_3); // 输出选项对输入无效，但可以保持一致
    gpio_af_set(GPIOA, GPIO_AF_2, GPIO_PIN_3); // PA3的AF2是USART1_RX

    /* USART1 参数配置 */
    usart_deinit(USART1); // 复位USART1到默认值
    usart_baudrate_set(USART1, baudval); // 设置波特率
    usart_word_length_set(USART1, USART_WL_8BIT); // 8位数据字长
    usart_stop_bit_set(USART1, USART_STB_1BIT); // 1位停止位
    usart_parity_config(USART1, USART_PM_NONE); // 无奇偶校验
    usart_transmit_config(USART1, USART_TRANSMIT_ENABLE); // 使能发送
    usart_receive_config(USART1, USART_RECEIVE_ENABLE); // 使能接收
    usart_enable(USART1); // 使能USART1

    // 初始化时设置为接收模式
    rs485_receive_enable();
}

// 发送单个字节
void usart1_send_byte(uint8_t ch) {
    usart_data_transmit(USART1, ch); // 将数据写入发送数据寄存器
    // 等待发送数据寄存器空中断标志位（TBE）置位，表示发送缓冲区已空
    while (usart_flag_get(USART1, USART_FLAG_TBE) == RESET);
}

// 发送字符串 (包含RS485方向控制逻辑)
void usart1_send_string(uint8_t *str) {
    // 1. 清除TC标志位，确保干净的发送状态
    usart_flag_clear(USART1, USART_FLAG_TC);

    // 2. 切换到发送模式
    rs485_send_enable();

    uint32_t i = 0;
    while (str[i] != '\0') {
        usart1_send_byte(str[i++]);
    }

    // 3. ！！！非常重要！！！等待所有数据完全发送出去（包括移位寄存器中的数据）
    // USART_FLAG_TC (Transmission Complete) 标志表示发送移位寄存器中的最后一个位已经发出。
    // 如果不等待这个标志就切换为接收模式，可能会导致发送的数据不完整或被截断。
    while (usart_flag_get(USART1, USART_FLAG_TC) == RESET);

    // 4. 切换回接收模式
    rs485_receive_enable();
}

// 接收数据缓冲
#define RX_BUFFER_SIZE 256
uint8_t rx_buffer[RX_BUFFER_SIZE];
volatile uint16_t rx_count = 0;

// 使能接收中断
void usart1_enable_receive_interrupt(void) {
    nvic_irq_enable(USART1_IRQn, 0, 0); // 使能USART1中断，设置优先级 (这里优先级都设为0，实际应用中请合理分配)
    usart_interrupt_enable(USART1, USART_INT_RBNE); // 使能接收非空中断 (Receive Buffer Not Empty)
    // usart_interrupt_enable(USART1, USART_INT_IDLE); // 如果需要，使能空闲帧中断
}

// USART1中断处理函数
void USART1_IRQHandler(void) {
    if (usart_interrupt_flag_get(USART1, USART_INT_FLAG_RBNE) != RESET) {
        // 接收到数据
        if (rx_count < RX_BUFFER_SIZE) {
            rx_buffer[rx_count++] = usart_data_receive(USART1);
        } else {
            // 缓冲区溢出处理，例如清空缓冲区或设置错误标志
            usart_data_receive(USART1); // 读走数据以清除RBNE标志
        }
        usart_interrupt_flag_clear(USART1, USART_INT_FLAG_RBNE); // 清除接收中断标志
    }
    // 如果使用空闲帧中断来判断一帧数据结束
    // if (usart_interrupt_flag_get(USART1, USART_INT_FLAG_IDLE) != RESET) {
    //     usart_data_receive(USART1); // 读一次数据寄存器，清除IDLE标志
    //     // 在这里处理rx_buffer中的完整数据帧，并清零rx_count
    //     // 例如：process_received_data(rx_buffer, rx_count); rx_count = 0;
    // }
}

int main(void)
{
    SysInit();

    usart1_rs485_init(9600); // 初始化USART1为RS485模式，波特率9600
    usart1_enable_receive_interrupt(); // 使能中断接收

    // TfCardTest();

    while(1)
    {
        usart1_send_string((uint8_t *)"Hello from GD32F470VET6 via RS485!");
        delay_1ms(2000); // 延时2秒

        // 在主循环中检查并处理接收到的数据 (如果使用中断接收，这里只是一个示例逻辑)
        if (rx_count > 0) {
            if (strcmp((char *)rx_buffer, "test") == 0)
            {
                ucLed[1] = 1;
            }
            // 简单打印接收到的数据 (假设您有printf重定向到调试串口或VOFA+)
            // for(uint16_t i = 0; i < rx_count; i++) {
            //     printf("%c", rx_buffer[i]);
            // }
            // printf("\r\n");
            // 清零接收计数器，准备接收下一帧数据
            rx_count = 0;
        }
        TaskExeution();
    }
}
