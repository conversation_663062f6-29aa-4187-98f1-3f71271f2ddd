Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    gd32f4xx_it.o(i.SDIO_IRQHandler) refers to sdio_sdcard.o(i.sd_interrupts_process) for sd_interrupts_process
    gd32f4xx_it.o(i.SysTick_Handler) refers to systick.o(i.delay_decrement) for delay_decrement
    gd32f4xx_it.o(i.SysTick_Handler) refers to gd32f470vet6_bsp.o(.data) for uwTick
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_disable) for usart_interrupt_disable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_get) for dma_transfer_number_get
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to rt_memclr.o(.text) for __aeabi_memclr
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f470vet6_bsp.o(.bss) for usart0_rx_buffer_dma
    gd32f4xx_it.o(i.USART0_IRQHandler) refers to gd32f470vet6_bsp.o(.data) for usart0_rx_flag
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_circulation_enable) for dma_circulation_enable
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f470vet6_bsp.o(i.AdcDmaInit) refers to gd32f470vet6_bsp.o(.bss) for adc0_value
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_clock_config) for adc_clock_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f470vet6_bsp.o(i.AdcDmaInit) for AdcDmaInit
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_sync_mode_config) for adc_sync_mode_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_special_function_config) for adc_special_function_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_data_alignment_config) for adc_data_alignment_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_channel_length_config) for adc_channel_length_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_routine_channel_config) for adc_routine_channel_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_external_trigger_source_config) for adc_external_trigger_source_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_external_trigger_config) for adc_external_trigger_config
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_dma_request_after_last_enable) for adc_dma_request_after_last_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_dma_mode_enable) for adc_dma_mode_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_enable) for adc_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to systick.o(i.delay_1ms) for delay_1ms
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_calibration_enable) for adc_calibration_enable
    gd32f470vet6_bsp.o(i.AdcPeriphInit) refers to gd32f4xx_adc.o(i.adc_software_trigger_enable) for adc_software_trigger_enable
    gd32f470vet6_bsp.o(i.BtnGetState) refers to gd32f4xx_gpio.o(i.gpio_input_bit_get) for gpio_input_bit_get
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to ebtn.o(i.ebtn_init) for ebtn_init
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to ebtn.o(i.ebtn_combo_btn_add_btn) for ebtn_combo_btn_add_btn
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to ebtn.o(i.ebtn_set_config) for ebtn_set_config
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to ebtn.o(i.ebtn_set_combo_suppress_threshold) for ebtn_set_combo_suppress_threshold
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to btn_app.o(i.BtnEventCallback) for BtnEventCallback
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to gd32f470vet6_bsp.o(i.BtnGetState) for BtnGetState
    gd32f470vet6_bsp.o(i.BtnPeriphInit) refers to gd32f470vet6_bsp.o(.data) for btns_combo
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd32f4xx_spi.o(i.spi_init) for spi_init
    gd32f470vet6_bsp.o(i.FlashPeriphInit) refers to gd25qxx.o(i.spi_flash_init) for spi_flash_init
    gd32f470vet6_bsp.o(i.LedDisp) refers to gd32f4xx_gpio.o(i.gpio_bit_write) for gpio_bit_write
    gd32f470vet6_bsp.o(i.LedDisp) refers to gd32f470vet6_bsp.o(.data) for temp_old
    gd32f470vet6_bsp.o(i.LedPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.LedPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.LedPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_single_data_para_struct_init) for dma_single_data_para_struct_init
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_circulation_disable) for dma_circulation_disable
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd32f470vet6_bsp.o(i.OledDmaInit) refers to gd32f470vet6_bsp.o(.data) for oled_data_buffer
    gd32f470vet6_bsp.o(i.OledDrawStr) refers to vsnprintf.o(.text) for vsnprintf
    gd32f470vet6_bsp.o(i.OledDrawStr) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_i2c.o(i.i2c_clock_config) for i2c_clock_config
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_i2c.o(i.i2c_mode_addr_config) for i2c_mode_addr_config
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_i2c.o(i.i2c_enable) for i2c_enable
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to gd32f470vet6_bsp.o(i.OledDmaInit) for OledDmaInit
    gd32f470vet6_bsp.o(i.OledPeriphInit) refers to oled.o(i.OLED_Init) for OLED_Init
    gd32f470vet6_bsp.o(i.ReadRtc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gd32f470vet6_bsp.o(i.ReadRtc) refers to gd32f4xx_rtc.o(i.rtc_current_time_get) for rtc_current_time_get
    gd32f470vet6_bsp.o(i.ReadRtc) refers to gd32f470vet6_bsp.o(i.BcdToDec) for BcdToDec
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f4xx_pmu.o(i.pmu_backup_write_enable) for pmu_backup_write_enable
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(i.RtcPreConfig) for RtcPreConfig
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(i.SetRtc) for SetRtc
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear) for rcu_all_reset_flag_clear
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(.data) for rtcsrc_flag
    gd32f470vet6_bsp.o(i.RtcPeriphInit) refers to gd32f470vet6_bsp.o(.bss) for ucRtc
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rcu.o(i.rcu_rtc_clock_config) for rcu_rtc_clock_config
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f470vet6_bsp.o(i.RtcPreConfig) refers to gd32f470vet6_bsp.o(.data) for prescaler_s
    gd32f470vet6_bsp.o(i.SetRtc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gd32f470vet6_bsp.o(i.SetRtc) refers to gd32f470vet6_bsp.o(i.DecToBcd) for DecToBcd
    gd32f470vet6_bsp.o(i.SetRtc) refers to gd32f4xx_rtc.o(i.rtc_init) for rtc_init
    gd32f470vet6_bsp.o(i.SetRtc) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    gd32f470vet6_bsp.o(i.SetRtc) refers to gd32f470vet6_bsp.o(.data) for prescaler_a
    gd32f470vet6_bsp.o(i.SysInit) refers to systick.o(i.systick_config) for systick_config
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.LedPeriphInit) for LedPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.BtnPeriphInit) for BtnPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.AdcPeriphInit) for AdcPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.Usart0PeriphInit) for Usart0PeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.OledPeriphInit) for OledPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.FlashPeriphInit) for FlashPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.RtcPeriphInit) for RtcPeriphInit
    gd32f470vet6_bsp.o(i.SysInit) refers to gd32f470vet6_bsp.o(i.TfPeriphInit) for TfPeriphInit
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to diskio.o(i.disk_initialize) for disk_initialize
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to ff.o(i.f_mount) for f_mount
    gd32f470vet6_bsp.o(i.TfPeriphInit) refers to gd32f470vet6_bsp.o(.bss) for fs
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_circulation_disable) for dma_circulation_disable
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd32f470vet6_bsp.o(i.Usart0DmaInit) refers to gd32f470vet6_bsp.o(.bss) for usart0_rx_buffer_dma
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_dma_receive_config) for usart_dma_receive_config
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f470vet6_bsp.o(i.Usart0DmaInit) for Usart0DmaInit
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    gd32f470vet6_bsp.o(i.Usart0PeriphInit) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    gd32f470vet6_bsp.o(i.Usart0Printf) refers to vsnprintf.o(.text) for vsnprintf
    gd32f470vet6_bsp.o(i.Usart0Printf) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    gd32f470vet6_bsp.o(i.Usart0Printf) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    gd32f470vet6_bsp.o(i.Usart0Printf) refers to gd32f470vet6_bsp.o(.data) for uwTick
    gd32f470vet6_bsp.o(.data) refers to usart_app.o(i.Usart0Task) for Usart0Task
    gd32f470vet6_bsp.o(.data) refers to btn_app.o(i.BtnTask) for BtnTask
    gd32f470vet6_bsp.o(.data) refers to adc_app.o(i.AdcTask) for AdcTask
    gd32f470vet6_bsp.o(.data) refers to led_app.o(i.LedTask) for LedTask
    gd32f470vet6_bsp.o(.data) refers to rtc_app.o(i.RtcTask) for RtcTask
    gd32f470vet6_bsp.o(.data) refers to gd32f470vet6_bsp.o(.constdata) for defaul_ebtn_param
    main.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_get) for usart_interrupt_flag_get
    main.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_data_receive) for usart_data_receive
    main.o(i.USART1_IRQHandler) refers to gd32f4xx_usart.o(i.usart_interrupt_flag_clear) for usart_interrupt_flag_clear
    main.o(i.USART1_IRQHandler) refers to main.o(.data) for rx_count
    main.o(i.USART1_IRQHandler) refers to main.o(.bss) for rx_buffer
    main.o(i.main) refers to gd32f470vet6_bsp.o(i.SysInit) for SysInit
    main.o(i.main) refers to main.o(i.usart1_rs485_init) for usart1_rs485_init
    main.o(i.main) refers to main.o(i.usart1_enable_receive_interrupt) for usart1_enable_receive_interrupt
    main.o(i.main) refers to main.o(i.usart1_send_string) for usart1_send_string
    main.o(i.main) refers to systick.o(i.delay_1ms) for delay_1ms
    main.o(i.main) refers to strcmpv7m.o(.text) for strcmp
    main.o(i.main) refers to scheduler.o(i.TaskExeution) for TaskExeution
    main.o(i.main) refers to main.o(.data) for rx_count
    main.o(i.main) refers to main.o(.bss) for rx_buffer
    main.o(i.main) refers to gd32f470vet6_bsp.o(.data) for ucLed
    main.o(i.rs485_receive_enable) refers to systick.o(i.delay_1ms) for delay_1ms
    main.o(i.rs485_send_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    main.o(i.rs485_send_enable) refers to systick.o(i.delay_1ms) for delay_1ms
    main.o(i.usart1_enable_receive_interrupt) refers to gd32f4xx_misc.o(i.nvic_irq_enable) for nvic_irq_enable
    main.o(i.usart1_enable_receive_interrupt) refers to gd32f4xx_usart.o(i.usart_interrupt_enable) for usart_interrupt_enable
    main.o(i.usart1_rs485_init) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    main.o(i.usart1_rs485_init) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    main.o(i.usart1_rs485_init) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    main.o(i.usart1_rs485_init) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    main.o(i.usart1_rs485_init) refers to gd32f4xx_usart.o(i.usart_deinit) for usart_deinit
    main.o(i.usart1_rs485_init) refers to gd32f4xx_usart.o(i.usart_baudrate_set) for usart_baudrate_set
    main.o(i.usart1_rs485_init) refers to gd32f4xx_usart.o(i.usart_word_length_set) for usart_word_length_set
    main.o(i.usart1_rs485_init) refers to gd32f4xx_usart.o(i.usart_stop_bit_set) for usart_stop_bit_set
    main.o(i.usart1_rs485_init) refers to gd32f4xx_usart.o(i.usart_parity_config) for usart_parity_config
    main.o(i.usart1_rs485_init) refers to gd32f4xx_usart.o(i.usart_transmit_config) for usart_transmit_config
    main.o(i.usart1_rs485_init) refers to gd32f4xx_usart.o(i.usart_receive_config) for usart_receive_config
    main.o(i.usart1_rs485_init) refers to gd32f4xx_usart.o(i.usart_enable) for usart_enable
    main.o(i.usart1_rs485_init) refers to main.o(i.rs485_receive_enable) for rs485_receive_enable
    main.o(i.usart1_send_byte) refers to gd32f4xx_usart.o(i.usart_data_transmit) for usart_data_transmit
    main.o(i.usart1_send_byte) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    main.o(i.usart1_send_string) refers to main.o(i.rs485_send_enable) for rs485_send_enable
    main.o(i.usart1_send_string) refers to main.o(i.usart1_send_byte) for usart1_send_byte
    main.o(i.usart1_send_string) refers to gd32f4xx_usart.o(i.usart_flag_get) for usart_flag_get
    main.o(i.usart1_send_string) refers to main.o(i.rs485_receive_enable) for rs485_receive_enable
    scheduler.o(i.TaskExeution) refers to gd32f470vet6_bsp.o(.data) for schedul_task
    systick.o(i.delay_1ms) refers to systick.o(.data) for delay
    systick.o(i.delay_decrement) refers to systick.o(.data) for delay
    systick.o(i.systick_config) refers to systick.o(i.NVIC_SetPriority) for NVIC_SetPriority
    systick.o(i.systick_config) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    adc_app.o(i.AdcTask) refers to gd32f470vet6_bsp.o(.bss) for adc0_value
    adc_app.o(i.AdcTask) refers to gd32f470vet6_bsp.o(.data) for adc0_voltage
    led_app.o(i.LedTask) refers to gd32f470vet6_bsp.o(i.LedDisp) for LedDisp
    led_app.o(i.LedTask) refers to gd32f470vet6_bsp.o(.data) for ucLed
    btn_app.o(i.BtnEventCallback) refers to gd32f470vet6_bsp.o(.data) for ucLed
    btn_app.o(i.BtnTask) refers to ebtn.o(i.ebtn_process) for ebtn_process
    btn_app.o(i.BtnTask) refers to gd32f470vet6_bsp.o(.data) for uwTick
    oled_app.o(i.OledTask) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled_app.o(i.OledTask) refers to gd32f470vet6_bsp.o(i.OledDrawStr) for OledDrawStr
    oled_app.o(i.OledTask) refers to gd32f470vet6_bsp.o(.data) for adc0_voltage
    rtc_app.o(i.RtcTask) refers to gd32f470vet6_bsp.o(i.ReadRtc) for ReadRtc
    rtc_app.o(i.RtcTask) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    rtc_app.o(i.RtcTask) refers to gd32f470vet6_bsp.o(.bss) for ucRtc
    rtc_app.o(i.RtcTask) refers to gd32f470vet6_bsp.o(.data) for uwTick
    tf_app.o(i.TfCardTest) refers to diskio.o(i.disk_initialize) for disk_initialize
    tf_app.o(i.TfCardTest) refers to tf_app.o(i.card_info_get) for card_info_get
    tf_app.o(i.TfCardTest) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_mount) for f_mount
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_open) for f_open
    tf_app.o(i.TfCardTest) refers to __2sprintf.o(.text) for __2sprintf
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_write) for f_write
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_close) for f_close
    tf_app.o(i.TfCardTest) refers to ff.o(i.f_read) for f_read
    tf_app.o(i.TfCardTest) refers to tf_app.o(i.memory_compare) for memory_compare
    tf_app.o(i.TfCardTest) refers to gd32f470vet6_bsp.o(.bss) for fs
    tf_app.o(i.TfCardTest) refers to tf_app.o(.bss) for fdst
    tf_app.o(i.TfCardTest) refers to tf_app.o(.data) for result
    tf_app.o(i.card_info_get) refers to sdio_sdcard.o(i.sd_card_information_get) for sd_card_information_get
    tf_app.o(i.card_info_get) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    tf_app.o(i.card_info_get) refers to sdio_sdcard.o(i.sd_card_capacity_get) for sd_card_capacity_get
    tf_app.o(i.card_info_get) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    usart_app.o(i.Usart0Task) refers to gd32f470vet6_bsp.o(i.Usart0Printf) for Usart0Printf
    usart_app.o(i.Usart0Task) refers to rt_memclr.o(.text) for __aeabi_memclr
    usart_app.o(i.Usart0Task) refers to gd32f470vet6_bsp.o(.data) for usart0_rx_flag
    usart_app.o(i.Usart0Task) refers to gd32f470vet6_bsp.o(.bss) for usart0_rx_buffer_proc
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_get_current_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_get_current_state) for ebtn_get_current_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_num_bits_set) for bit_array_num_bits_set
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_or) for bit_array_or
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_combo_suppress_threshold) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.ebtn_set_config) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_get_combo_btn_by_key_id) refers to ebtn.o(.bss) for ebtn_default
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.ebtn_timer_sub) for ebtn_timer_sub
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.prv_get_combo_btn_by_key_id) for prv_get_combo_btn_by_key_id
    ebtn.o(i.prv_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for ebtn_default
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    oled.o(i.I2C_Bus_Reset) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_deinit) for i2c_deinit
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_clock_config) for i2c_clock_config
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_mode_addr_config) for i2c_mode_addr_config
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_enable) for i2c_enable
    oled.o(i.I2C_Bus_Reset) refers to gd32f4xx_i2c.o(i.i2c_ack_config) for i2c_ack_config
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for initcmd1
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for Hzk
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for Hzb
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    oled.o(i.OLED_Write_cmd) refers to oled.o(i.I2C_Bus_Reset) for I2C_Bus_Reset
    oled.o(i.OLED_Write_cmd) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_memory_address_config) for dma_memory_address_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_dma_config) for i2c_dma_config
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    oled.o(i.OLED_Write_cmd) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    oled.o(i.OLED_Write_cmd) refers to gd32f470vet6_bsp.o(.data) for oled_cmd_buffer
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_flag_get) for i2c_flag_get
    oled.o(i.OLED_Write_data) refers to oled.o(i.I2C_Bus_Reset) for I2C_Bus_Reset
    oled.o(i.OLED_Write_data) refers to systick.o(i.delay_1ms) for delay_1ms
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_start_on_bus) for i2c_start_on_bus
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_master_addressing) for i2c_master_addressing
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_flag_clear) for i2c_flag_clear
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_memory_address_config) for dma_memory_address_config
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_transfer_number_config) for dma_transfer_number_config
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_dma_config) for i2c_dma_config
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    oled.o(i.OLED_Write_data) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    oled.o(i.OLED_Write_data) refers to gd32f4xx_i2c.o(i.i2c_stop_on_bus) for i2c_stop_on_bus
    oled.o(i.OLED_Write_data) refers to gd32f470vet6_bsp.o(.data) for oled_data_buffer
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_buffer_read) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_buffer_write) refers to gd25qxx.o(i.spi_flash_page_write) for spi_flash_page_write
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_bulk_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_init) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_init) refers to gd32f4xx_spi.o(i.spi_enable) for spi_enable
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_page_write) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_page_write) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_read_id) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_read_id) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_write_enable) for spi_flash_write_enable
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_sector_erase) refers to gd25qxx.o(i.spi_flash_wait_for_write_end) for spi_flash_wait_for_write_end
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_send_byte_dma) refers to gd32f470vet6_bsp.o(.bss) for spi1_tx_buffer
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_send_halfword_dma) refers to gd32f470vet6_bsp.o(.bss) for spi1_tx_buffer
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_start_read_sequence) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_single_data_mode_init) for dma_single_data_mode_init
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_enable) for spi_dma_enable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_spi.o(i.spi_dma_disable) for spi_dma_disable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_transmit_receive_dma) refers to gd32f470vet6_bsp.o(.bss) for spi1_tx_buffer
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    gd25qxx.o(i.spi_flash_wait_for_dma_end) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_wait_for_write_end) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    gd25qxx.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_reset) for gpio_bit_reset
    gd25qxx.o(i.spi_flash_write_enable) refers to gd25qxx.o(i.spi_flash_send_byte_dma) for spi_flash_send_byte_dma
    gd25qxx.o(i.spi_flash_write_enable) refers to gd32f4xx_gpio.o(i.gpio_bit_set) for gpio_bit_set
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.cmdsent_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_receive_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flag_clear) for dma_flag_clear
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_disable) for dma_channel_disable
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_deinit) for dma_deinit
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_multi_data_mode_init) for dma_multi_data_mode_init
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_flow_controller_config) for dma_flow_controller_config
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_subperipheral_select) for dma_channel_subperipheral_select
    sdio_sdcard.o(i.dma_transfer_config) refers to gd32f4xx_dma.o(i.dma_channel_enable) for dma_channel_enable
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_af_set) for gpio_af_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_mode_set) for gpio_mode_set
    sdio_sdcard.o(i.gpio_config) refers to gd32f4xx_gpio.o(i.gpio_output_options_set) for gpio_output_options_set
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r1_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r1_error_check) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.r2_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r3_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.r6_error_check) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.r7_error_check) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.rcu_config) refers to gd32f4xx_rcu.o(i.rcu_periph_clock_enable) for rcu_periph_clock_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_block_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_block_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_block_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(i.sd_bus_width_config) for sd_bus_width_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_bus_mode_config) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_bus_mode_config) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.sd_scr_get) for sd_scr_get
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_bus_width_config) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_bus_width_config) refers to sdio_sdcard.o(.data) for sd_scr
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_capacity_get) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_information_get) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_power_state_get) for sdio_power_state_get
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r2_error_check) for r2_error_check
    sdio_sdcard.o(i.sd_card_init) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(i.r6_error_check) for r6_error_check
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_card_init) refers to sdio_sdcard.o(.bss) for sd_cid
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_select_deselect) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_select_deselect) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_command_index_get) for sdio_command_index_get
    sdio_sdcard.o(i.sd_card_state_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(i.r1_error_type_check) for r1_error_type_check
    sdio_sdcard.o(i.sd_card_state_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_cardstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_cardstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_erase) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_erase) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.rcu_config) for rcu_config
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.gpio_config) for gpio_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_deinit) for sdio_deinit
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_power_on) for sd_power_on
    sdio_sdcard.o(i.sd_init) refers to sdio_sdcard.o(i.sd_card_init) for sd_card_init
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_init) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_get) for sdio_interrupt_flag_get
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(i.sd_transfer_stop) for sd_transfer_stop
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear) for sdio_interrupt_flag_clear
    sdio_sdcard.o(i.sd_interrupts_process) refers to gd32f4xx_sdio.o(i.sdio_interrupt_disable) for sdio_interrupt_disable
    sdio_sdcard.o(i.sd_interrupts_process) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.bss) for sd_csd
    sdio_sdcard.o(i.sd_lock_unlock) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(i.dma_receive_config) for dma_receive_config
    sdio_sdcard.o(i.sd_multiblocks_read) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_read) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_disable) for sdio_dsm_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_disable) for sdio_dma_disable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_datablocksize_get) for sd_datablocksize_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_data_write) for sdio_data_write
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_interrupt_enable) for sdio_interrupt_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_sdio.o(i.sdio_dma_enable) for sdio_dma_enable
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.dma_transfer_config) for dma_transfer_config
    sdio_sdcard.o(i.sd_multiblocks_write) refers to gd32f4xx_dma.o(i.dma_flag_get) for dma_flag_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(i.sd_card_state_get) for sd_card_state_get
    sdio_sdcard.o(i.sd_multiblocks_write) refers to sdio_sdcard.o(.data) for transerror
    sdio_sdcard.o(i.sd_power_off) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_config) for sdio_clock_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_bus_mode_set) for sdio_bus_mode_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_hardware_clock_disable) for sdio_hardware_clock_disable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_power_state_set) for sdio_power_state_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_clock_enable) for sdio_clock_enable
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.cmdsent_error_check) for cmdsent_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r7_error_check) for r7_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(i.r3_error_check) for r3_error_check
    sdio_sdcard.o(i.sd_power_on) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_power_on) refers to sdio_sdcard.o(.data) for cardtype
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_scr_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_response_get) for sdio_response_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_config) for sdio_data_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_transfer_config) for sdio_data_transfer_config
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_dsm_enable) for sdio_dsm_enable
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_data_read) for sdio_data_read
    sdio_sdcard.o(i.sd_sdstatus_get) refers to gd32f4xx_sdio.o(i.sdio_flag_clear) for sdio_flag_clear
    sdio_sdcard.o(i.sd_sdstatus_get) refers to sdio_sdcard.o(.data) for sd_rca
    sdio_sdcard.o(i.sd_transfer_mode_config) refers to sdio_sdcard.o(.data) for transmode
    sdio_sdcard.o(i.sd_transfer_state_get) refers to gd32f4xx_sdio.o(i.sdio_flag_get) for sdio_flag_get
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_command_response_config) for sdio_command_response_config
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_wait_type_set) for sdio_wait_type_set
    sdio_sdcard.o(i.sd_transfer_stop) refers to gd32f4xx_sdio.o(i.sdio_csm_enable) for sdio_csm_enable
    sdio_sdcard.o(i.sd_transfer_stop) refers to sdio_sdcard.o(i.r1_error_check) for r1_error_check
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_init) for sd_init
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_card_information_get) for sd_card_information_get
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_card_select_deselect) for sd_card_select_deselect
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_cardstatus_get) for sd_cardstatus_get
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_bus_mode_config) for sd_bus_mode_config
    diskio.o(i.disk_initialize) refers to sdio_sdcard.o(i.sd_transfer_mode_config) for sd_transfer_mode_config
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.sd_block_read) for sd_block_read
    diskio.o(i.disk_read) refers to sdio_sdcard.o(i.sd_multiblocks_read) for sd_multiblocks_read
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.sd_block_write) for sd_block_write
    diskio.o(i.disk_write) refers to sdio_sdcard.o(i.sd_multiblocks_write) for sd_multiblocks_write
    ff.o(i.check_fs) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_status) for disk_status
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_initialize) for disk_initialize
    ff.o(i.chk_mounted) refers to ff.o(i.check_fs) for check_fs
    ff.o(i.chk_mounted) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.chk_mounted) refers to ff.o(.data) for FatFs
    ff.o(i.cmp_lfn) refers to unicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.cmp_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.create_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.create_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.create_name) refers to unicode.o(i.ff_convert) for ff_convert
    ff.o(i.create_name) refers to ff.o(i.chk_chr) for chk_chr
    ff.o(i.create_name) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.create_name) refers to unicode.o(i.ff_wtoupper) for ff_wtoupper
    ff.o(i.dir_find) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_find) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_find) refers to ff.o(i.cmp_lfn) for cmp_lfn
    ff.o(i.dir_find) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_find) refers to ff.o(i.mem_cmp) for mem_cmp
    ff.o(i.dir_find) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_next) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_next) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.dir_next) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_next) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_next) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.dir_read) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_read) refers to ff.o(i.pick_lfn) for pick_lfn
    ff.o(i.dir_read) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_read) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.dir_register) refers to ff.o(i.gen_numname) for gen_numname
    ff.o(i.dir_register) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.dir_register) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_register) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_register) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_register) refers to ff.o(i.sum_sfn) for sum_sfn
    ff.o(i.dir_register) refers to ff.o(i.fit_lfn) for fit_lfn
    ff.o(i.dir_register) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.dir_remove) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.dir_remove) refers to ff.o(i.move_window) for move_window
    ff.o(i.dir_remove) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.dir_sdi) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.dir_sdi) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_chmod) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_chmod) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_chmod) refers to ff.o(i.sync) for sync
    ff.o(i.f_close) refers to ff.o(i.f_sync) for f_sync
    ff.o(i.f_getfree) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_getfree) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_getfree) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_gets) refers to ff.o(i.f_read) for f_read
    ff.o(i.f_lseek) refers to ff.o(i.validate) for validate
    ff.o(i.f_lseek) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_lseek) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_lseek) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_lseek) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_lseek) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_mkdir) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_mkdir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_mkdir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_mkdir) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_mkdir) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_mkdir) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_mkdir) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.f_mkdir) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_mkdir) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_mkdir) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_mkdir) refers to ff.o(i.sync) for sync
    ff.o(i.f_mount) refers to ff.o(.data) for FatFs
    ff.o(i.f_open) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_open) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_open) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_open) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_open) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_open) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_opendir) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_opendir) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_opendir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_printf) refers to ff.o(i.f_putc) for f_putc
    ff.o(i.f_printf) refers to ff.o(i.f_puts) for f_puts
    ff.o(i.f_putc) refers to ff.o(i.f_write) for f_write
    ff.o(i.f_puts) refers to ff.o(i.f_putc) for f_putc
    ff.o(i.f_read) refers to ff.o(i.validate) for validate
    ff.o(i.f_read) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_read) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_read) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.f_read) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_read) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_readdir) refers to ff.o(i.validate) for validate
    ff.o(i.f_readdir) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_readdir) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_readdir) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_readdir) refers to ff.o(i.dir_next) for dir_next
    ff.o(i.f_rename) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_rename) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_rename) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_rename) refers to ff.o(i.dir_register) for dir_register
    ff.o(i.f_rename) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_rename) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_rename) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_rename) refers to ff.o(i.sync) for sync
    ff.o(i.f_stat) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_stat) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_stat) refers to ff.o(i.get_fileinfo) for get_fileinfo
    ff.o(i.f_sync) refers to ff.o(i.validate) for validate
    ff.o(i.f_sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.f_sync) refers to diskio.o(i.get_fattime) for get_fattime
    ff.o(i.f_sync) refers to ff.o(i.sync) for sync
    ff.o(i.f_truncate) refers to ff.o(i.validate) for validate
    ff.o(i.f_truncate) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_truncate) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.f_truncate) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.f_unlink) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_unlink) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_unlink) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_unlink) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.f_unlink) refers to ff.o(i.dir_read) for dir_read
    ff.o(i.f_unlink) refers to ff.o(i.dir_remove) for dir_remove
    ff.o(i.f_unlink) refers to ff.o(i.remove_chain) for remove_chain
    ff.o(i.f_unlink) refers to ff.o(i.sync) for sync
    ff.o(i.f_utime) refers to ff.o(i.chk_mounted) for chk_mounted
    ff.o(i.f_utime) refers to ff.o(i.follow_path) for follow_path
    ff.o(i.f_utime) refers to ff.o(i.sync) for sync
    ff.o(i.f_write) refers to ff.o(i.validate) for validate
    ff.o(i.f_write) refers to ff.o(i.create_chain) for create_chain
    ff.o(i.f_write) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.f_write) refers to ff.o(i.clust2sect) for clust2sect
    ff.o(i.f_write) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.f_write) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.fit_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.follow_path) refers to ff.o(i.dir_sdi) for dir_sdi
    ff.o(i.follow_path) refers to ff.o(i.create_name) for create_name
    ff.o(i.follow_path) refers to ff.o(i.dir_find) for dir_find
    ff.o(i.gen_numname) refers to ff.o(i.mem_cpy) for mem_cpy
    ff.o(i.get_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.get_fileinfo) refers to unicode.o(i.ff_convert) for ff_convert
    ff.o(i.move_window) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.move_window) refers to diskio.o(i.disk_read) for disk_read
    ff.o(i.pick_lfn) refers to ff.o(.constdata) for LfnOfs
    ff.o(i.put_fat) refers to ff.o(i.move_window) for move_window
    ff.o(i.remove_chain) refers to ff.o(i.get_fat) for get_fat
    ff.o(i.remove_chain) refers to ff.o(i.put_fat) for put_fat
    ff.o(i.sync) refers to ff.o(i.move_window) for move_window
    ff.o(i.sync) refers to ff.o(i.mem_set) for mem_set
    ff.o(i.sync) refers to diskio.o(i.disk_write) for disk_write
    ff.o(i.sync) refers to diskio.o(i.disk_ioctl) for disk_ioctl
    ff.o(i.validate) refers to diskio.o(i.disk_status) for disk_status
    unicode.o(i.ff_wtoupper) refers to unicode.o(.constdata) for tbl_lower
    system_gd32f4xx.o(i.SystemCoreClockUpdate) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i._soft_delay_) for _soft_delay_
    system_gd32f4xx.o(i.SystemInit) refers to system_gd32f4xx.o(i.system_clock_config) for system_clock_config
    system_gd32f4xx.o(i.system_clock_config) refers to system_gd32f4xx.o(i.system_clock_240m_25m_hxtal) for system_clock_240m_25m_hxtal
    startup_gd32f450_470.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.USART0_IRQHandler) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to main.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(i.SDIO_IRQHandler) for SDIO_IRQHandler
    startup_gd32f450_470.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(i.SystemInit) for SystemInit
    startup_gd32f450_470.o(.text) refers to __main.o(!!!main) for __main
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(HEAP) for Heap_Mem
    startup_gd32f450_470.o(.text) refers to startup_gd32f450_470.o(STACK) for Stack_Mem
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_adc.o(i.adc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_debug_freeze_disable) refers to gd32f4xx_dbg.o(i.dbg_periph_disable) for dbg_periph_disable
    gd32f4xx_can.o(i.can_debug_freeze_enable) refers to gd32f4xx_dbg.o(i.dbg_periph_enable) for dbg_periph_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_can.o(i.can_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_receive_message_length_get) for can_receive_message_length_get
    gd32f4xx_can.o(i.can_interrupt_flag_get) refers to gd32f4xx_can.o(i.can_error_get) for can_error_get
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ctc.o(i.ctc_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dac.o(i.dac_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_dci.o(i.dci_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_enet.o(i.enet_deinit) refers to gd32f4xx_enet.o(i.enet_initpara_reset) for enet_initpara_reset
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_tx_disable) for enet_tx_disable
    gd32f4xx_enet.o(i.enet_disable) refers to gd32f4xx_enet.o(i.enet_rx_disable) for enet_rx_disable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_tx_enable) for enet_tx_enable
    gd32f4xx_enet.o(i.enet_enable) refers to gd32f4xx_enet.o(i.enet_rx_enable) for enet_rx_enable
    gd32f4xx_enet.o(i.enet_frame_receive) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_frame_transmit) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_config) for enet_phy_config
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_default_init) for enet_default_init
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_init) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_config) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_initpara_reset) refers to gd32f4xx_enet.o(.bss) for enet_initpara
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phy_config) refers to gd32f4xx_enet.o(i.enet_delay) for enet_delay
    gd32f4xx_enet.o(i.enet_phyloopback_disable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_phyloopback_enable) refers to gd32f4xx_enet.o(i.enet_phy_write_read) for enet_phy_write_read
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.bss) for txdesc_tab
    gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode) refers to gd32f4xx_enet.o(.data) for dma_current_txdesc
    gd32f4xx_enet.o(i.enet_registers_get) refers to gd32f4xx_enet.o(.constdata) for enet_reg_tab
    gd32f4xx_enet.o(i.enet_rxframe_drop) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(i.enet_rxframe_drop) for enet_rxframe_drop
    gd32f4xx_enet.o(i.enet_rxframe_size_get) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_rxprocess_check_recovery) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    gd32f4xx_enet.o(i.enet_tx_disable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_enet.o(i.enet_tx_enable) refers to gd32f4xx_enet.o(i.enet_txfifo_flush) for enet_txfifo_flush
    gd32f4xx_fmc.o(i.fmc_bank0_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_bank1_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_byte_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_halfword_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_mass_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_page_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_ready_wait) refers to gd32f4xx_fmc.o(i.fmc_state_get) for fmc_state_get
    gd32f4xx_fmc.o(i.fmc_sector_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.fmc_word_program) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_drp_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_erase) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_security_protection_config) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_user_write) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_disable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_fmc.o(i.ob_write_protection_enable) refers to gd32f4xx_fmc.o(i.fmc_ready_wait) for fmc_ready_wait
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_gpio.o(i.gpio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_i2c.o(i.i2c_clock_config) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(i.i2c_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(i.ipa_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_iref.o(i.iref_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_misc.o(i.nvic_irq_enable) refers to gd32f4xx_misc.o(i.nvic_priority_group_set) for nvic_priority_group_set
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(i.pmu_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_pmu.o(i.pmu_highdriver_switch_select) refers to gd32f4xx_pmu.o(i.pmu_flag_get) for pmu_flag_get
    gd32f4xx_pmu.o(i.pmu_to_deepsleepmode) refers to gd32f4xx_pmu.o(.bss) for reg_snap
    gd32f4xx_rcu.o(i.rcu_deinit) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_rcu.o(i.rcu_osci_stab_wait) refers to gd32f4xx_rcu.o(i.rcu_flag_get) for rcu_flag_get
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_config) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_deinit) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_init) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_disable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_enter) for rtc_init_mode_enter
    gd32f4xx_rtc.o(i.rtc_refclock_detection_enable) refers to gd32f4xx_rtc.o(i.rtc_init_mode_exit) for rtc_init_mode_exit
    gd32f4xx_rtc.o(i.rtc_second_adjust) refers to gd32f4xx_rtc.o(i.rtc_register_sync_wait) for rtc_register_sync_wait
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_sdio.o(i.sdio_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_on) for rcu_osci_on
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_osci_stab_wait) for rcu_osci_stab_wait
    gd32f4xx_spi.o(i.i2s_psc_config) refers to gd32f4xx_rcu.o(i.rcu_i2s_clock_config) for rcu_i2s_clock_config
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_spi.o(i.spi_i2s_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(i.syscfg_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_timer.o(i.timer_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_timer.o(i.timer_external_clock_mode0_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_clock_mode1_config) refers to gd32f4xx_timer.o(i.timer_external_trigger_config) for timer_external_trigger_config
    gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_timer.o(i.timer_input_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_input_pwm_capture_config) refers to gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config) for timer_channel_input_capture_prescaler_config
    gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config) refers to gd32f4xx_timer.o(i.timer_input_trigger_source_select) for timer_input_trigger_source_select
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_tli.o(i.tli_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_trng.o(i.trng_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_usart.o(i.usart_baudrate_set) refers to gd32f4xx_rcu.o(i.rcu_clock_freq_get) for rcu_clock_freq_get
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_usart.o(i.usart_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_enable) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(i.wwdgt_deinit) refers to gd32f4xx_rcu.o(i.rcu_periph_reset_disable) for rcu_periph_reset_disable
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_gd32f450_470.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing gd32f470vet6_bsp.o(.rev16_text), (4 bytes).
    Removing gd32f470vet6_bsp.o(.revsh_text), (4 bytes).
    Removing gd32f470vet6_bsp.o(i.OledDrawStr), (60 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing adc_app.o(.rev16_text), (4 bytes).
    Removing adc_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing btn_app.o(.rev16_text), (4 bytes).
    Removing btn_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(i.OledTask), (64 bytes).
    Removing rtc_app.o(.rev16_text), (4 bytes).
    Removing rtc_app.o(.revsh_text), (4 bytes).
    Removing tf_app.o(.rev16_text), (4 bytes).
    Removing tf_app.o(.revsh_text), (4 bytes).
    Removing tf_app.o(i.TfCardTest), (540 bytes).
    Removing tf_app.o(i.card_info_get), (928 bytes).
    Removing tf_app.o(i.memory_compare), (36 bytes).
    Removing tf_app.o(.bss), (876 bytes).
    Removing tf_app.o(.data), (16 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx), (32 bytes).
    Removing ebtn.o(i.ebtn_combo_register), (60 bytes).
    Removing ebtn.o(i.ebtn_get_btn_by_key_id), (80 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn_dyn), (12 bytes).
    Removing ebtn.o(i.ebtn_get_config), (12 bytes).
    Removing ebtn.o(i.ebtn_get_total_btn_cnt), (28 bytes).
    Removing ebtn.o(i.ebtn_is_btn_active), (20 bytes).
    Removing ebtn.o(i.ebtn_is_btn_in_process), (20 bytes).
    Removing ebtn.o(i.ebtn_is_in_process), (132 bytes).
    Removing ebtn.o(i.ebtn_register), (72 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(i.OLED_Allfill), (56 bytes).
    Removing oled.o(i.OLED_Display_Off), (22 bytes).
    Removing oled.o(i.OLED_Display_On), (22 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ShowChar), (156 bytes).
    Removing oled.o(i.OLED_ShowFloat), (352 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (100 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (184 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.OLED_ShowPic), (76 bytes).
    Removing oled.o(i.OLED_ShowStr), (58 bytes).
    Removing oled.o(.constdata), (2712 bytes).
    Removing gd25qxx.o(.rev16_text), (4 bytes).
    Removing gd25qxx.o(.revsh_text), (4 bytes).
    Removing gd25qxx.o(i.spi_flash_buffer_read), (84 bytes).
    Removing gd25qxx.o(i.spi_flash_buffer_write), (290 bytes).
    Removing gd25qxx.o(i.spi_flash_bulk_erase), (44 bytes).
    Removing gd25qxx.o(i.spi_flash_page_write), (92 bytes).
    Removing gd25qxx.o(i.spi_flash_read_id), (84 bytes).
    Removing gd25qxx.o(i.spi_flash_sector_erase), (68 bytes).
    Removing gd25qxx.o(i.spi_flash_send_byte_dma), (252 bytes).
    Removing gd25qxx.o(i.spi_flash_send_halfword_dma), (268 bytes).
    Removing gd25qxx.o(i.spi_flash_start_read_sequence), (48 bytes).
    Removing gd25qxx.o(i.spi_flash_transmit_receive_dma), (292 bytes).
    Removing gd25qxx.o(i.spi_flash_wait_for_dma_end), (44 bytes).
    Removing gd25qxx.o(i.spi_flash_wait_for_write_end), (56 bytes).
    Removing gd25qxx.o(i.spi_flash_write_enable), (36 bytes).
    Removing sdio_sdcard.o(.rev16_text), (4 bytes).
    Removing sdio_sdcard.o(.revsh_text), (4 bytes).
    Removing sdio_sdcard.o(i.dma_receive_config), (180 bytes).
    Removing sdio_sdcard.o(i.dma_transfer_config), (180 bytes).
    Removing sdio_sdcard.o(i.sd_block_read), (536 bytes).
    Removing sdio_sdcard.o(i.sd_block_write), (800 bytes).
    Removing sdio_sdcard.o(i.sd_card_capacity_get), (168 bytes).
    Removing sdio_sdcard.o(i.sd_card_state_get), (184 bytes).
    Removing sdio_sdcard.o(i.sd_datablocksize_get), (24 bytes).
    Removing sdio_sdcard.o(i.sd_erase), (324 bytes).
    Removing sdio_sdcard.o(i.sd_lock_unlock), (488 bytes).
    Removing sdio_sdcard.o(i.sd_multiblocks_read), (668 bytes).
    Removing sdio_sdcard.o(i.sd_multiblocks_write), (920 bytes).
    Removing sdio_sdcard.o(i.sd_power_off), (14 bytes).
    Removing sdio_sdcard.o(i.sd_sdstatus_get), (384 bytes).
    Removing sdio_sdcard.o(i.sd_transfer_state_get), (20 bytes).
    Removing diskio.o(.rev16_text), (4 bytes).
    Removing diskio.o(.revsh_text), (4 bytes).
    Removing diskio.o(i.disk_ioctl), (6 bytes).
    Removing diskio.o(i.disk_read), (80 bytes).
    Removing diskio.o(i.disk_status), (12 bytes).
    Removing diskio.o(i.disk_write), (80 bytes).
    Removing diskio.o(i.get_fattime), (4 bytes).
    Removing ff.o(i.check_fs), (144 bytes).
    Removing ff.o(i.chk_chr), (20 bytes).
    Removing ff.o(i.chk_mounted), (916 bytes).
    Removing ff.o(i.clust2sect), (26 bytes).
    Removing ff.o(i.cmp_lfn), (144 bytes).
    Removing ff.o(i.create_chain), (202 bytes).
    Removing ff.o(i.create_name), (624 bytes).
    Removing ff.o(i.dir_find), (222 bytes).
    Removing ff.o(i.dir_next), (280 bytes).
    Removing ff.o(i.dir_read), (190 bytes).
    Removing ff.o(i.dir_register), (396 bytes).
    Removing ff.o(i.dir_remove), (96 bytes).
    Removing ff.o(i.dir_sdi), (156 bytes).
    Removing ff.o(i.f_chmod), (92 bytes).
    Removing ff.o(i.f_close), (22 bytes).
    Removing ff.o(i.f_getfree), (276 bytes).
    Removing ff.o(i.f_gets), (78 bytes).
    Removing ff.o(i.f_lseek), (432 bytes).
    Removing ff.o(i.f_mkdir), (392 bytes).
    Removing ff.o(i.f_open), (372 bytes).
    Removing ff.o(i.f_opendir), (118 bytes).
    Removing ff.o(i.f_printf), (700 bytes).
    Removing ff.o(i.f_putc), (38 bytes).
    Removing ff.o(i.f_puts), (42 bytes).
    Removing ff.o(i.f_read), (462 bytes).
    Removing ff.o(i.f_readdir), (100 bytes).
    Removing ff.o(i.f_rename), (298 bytes).
    Removing ff.o(i.f_stat), (66 bytes).
    Removing ff.o(i.f_sync), (184 bytes).
    Removing ff.o(i.f_truncate), (156 bytes).
    Removing ff.o(i.f_unlink), (186 bytes).
    Removing ff.o(i.f_utime), (94 bytes).
    Removing ff.o(i.f_write), (526 bytes).
    Removing ff.o(i.fit_lfn), (128 bytes).
    Removing ff.o(i.follow_path), (158 bytes).
    Removing ff.o(i.gen_numname), (190 bytes).
    Removing ff.o(i.get_fat), (228 bytes).
    Removing ff.o(i.get_fileinfo), (316 bytes).
    Removing ff.o(i.mem_cmp), (38 bytes).
    Removing ff.o(i.mem_cpy), (26 bytes).
    Removing ff.o(i.mem_set), (20 bytes).
    Removing ff.o(i.move_window), (114 bytes).
    Removing ff.o(i.pick_lfn), (116 bytes).
    Removing ff.o(i.put_fat), (310 bytes).
    Removing ff.o(i.remove_chain), (104 bytes).
    Removing ff.o(i.sum_sfn), (32 bytes).
    Removing ff.o(i.sync), (202 bytes).
    Removing ff.o(i.validate), (42 bytes).
    Removing ff.o(.constdata), (13 bytes).
    Removing unicode.o(i.ff_convert), (20 bytes).
    Removing unicode.o(i.ff_wtoupper), (68 bytes).
    Removing unicode.o(.constdata), (376 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(i.SystemCoreClockUpdate), (272 bytes).
    Removing system_gd32f4xx.o(i.gd32f4xx_firmware_version_get), (6 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(i.adc_channel_16_to_18), (96 bytes).
    Removing gd32f4xx_adc.o(i.adc_deinit), (20 bytes).
    Removing gd32f4xx_adc.o(i.adc_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_discontinuous_mode_config), (82 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_mode_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_dma_request_after_last_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_end_of_conversion_config), (34 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_flag_get), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_config), (124 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_channel_offset_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_data_read), (46 bytes).
    Removing gd32f4xx_adc.o(i.adc_inserted_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_disable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_enable), (66 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_clear), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_config), (58 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_disable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_oversample_mode_enable), (14 bytes).
    Removing gd32f4xx_adc.o(i.adc_resolution_config), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_data_read), (8 bytes).
    Removing gd32f4xx_adc.o(i.adc_routine_software_startconv_flag_get), (16 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_delay_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_config), (36 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_disable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_dma_request_after_last_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_sync_routine_data_read), (12 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_disable), (50 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_sequence_channel_enable), (64 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_disable), (10 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_single_channel_enable), (24 bytes).
    Removing gd32f4xx_adc.o(i.adc_watchdog_threshold_config), (14 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(i.can1_filter_start_bank), (56 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_disable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_debug_freeze_enable), (44 bytes).
    Removing gd32f4xx_can.o(i.can_deinit), (52 bytes).
    Removing gd32f4xx_can.o(i.can_error_get), (12 bytes).
    Removing gd32f4xx_can.o(i.can_fifo_release), (32 bytes).
    Removing gd32f4xx_can.o(i.can_filter_init), (272 bytes).
    Removing gd32f4xx_can.o(i.can_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_flag_get), (30 bytes).
    Removing gd32f4xx_can.o(i.can_init), (290 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_disable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_enable), (8 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_can.o(i.can_interrupt_flag_get), (116 bytes).
    Removing gd32f4xx_can.o(i.can_message_receive), (228 bytes).
    Removing gd32f4xx_can.o(i.can_message_transmit), (336 bytes).
    Removing gd32f4xx_can.o(i.can_receive_error_number_get), (8 bytes).
    Removing gd32f4xx_can.o(i.can_receive_message_length_get), (26 bytes).
    Removing gd32f4xx_can.o(i.can_struct_para_init), (164 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_disable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_time_trigger_mode_enable), (48 bytes).
    Removing gd32f4xx_can.o(i.can_transmission_stop), (80 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_error_number_get), (10 bytes).
    Removing gd32f4xx_can.o(i.can_transmit_states), (124 bytes).
    Removing gd32f4xx_can.o(i.can_wakeup), (48 bytes).
    Removing gd32f4xx_can.o(i.can_working_mode_set), (168 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(i.crc_block_data_calculate), (36 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_data_register_reset), (20 bytes).
    Removing gd32f4xx_crc.o(i.crc_deinit), (24 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_read), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_free_data_register_write), (12 bytes).
    Removing gd32f4xx_crc.o(i.crc_single_data_calculate), (16 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_clock_limit_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_capture_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_direction_read), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_disable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_enable), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_counter_reload_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_deinit), (20 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_flag_get), (24 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_hardware_trim_mode_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_interrupt_flag_get), (56 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_irc48m_trim_value_read), (16 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_polarity_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_prescaler_config), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_refsource_signal_select), (28 bytes).
    Removing gd32f4xx_ctc.o(i.ctc_software_refsource_pulse_generate), (20 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_data_set), (48 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_disable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_output_buffer_enable), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_concurrent_software_trigger_enable), (12 bytes).
    Removing gd32f4xx_dac.o(i.dac_data_set), (64 bytes).
    Removing gd32f4xx_dac.o(i.dac_deinit), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_dma_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_flag_get), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_disable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_enable), (18 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_clear), (14 bytes).
    Removing gd32f4xx_dac.o(i.dac_interrupt_flag_get), (38 bytes).
    Removing gd32f4xx_dac.o(i.dac_lfsr_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_buffer_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_output_value_get), (22 bytes).
    Removing gd32f4xx_dac.o(i.dac_software_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_triangle_noise_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_disable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_enable), (26 bytes).
    Removing gd32f4xx_dac.o(i.dac_trigger_source_config), (40 bytes).
    Removing gd32f4xx_dac.o(i.dac_wave_mode_config), (40 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_deinit), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_id_get), (12 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_disable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_low_power_enable), (16 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_disable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_periph_enable), (32 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_disable), (20 bytes).
    Removing gd32f4xx_dbg.o(i.dbg_trace_pin_enable), (20 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_capture_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_crop_window_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_data_read), (12 bytes).
    Removing gd32f4xx_dci.o(i.dci_deinit), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_embedded_sync_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_flag_get), (36 bytes).
    Removing gd32f4xx_dci.o(i.dci_init), (52 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_disable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_enable), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_dci.o(i.dci_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_disable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_jpeg_enable), (20 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_config), (24 bytes).
    Removing gd32f4xx_dci.o(i.dci_sync_codes_unmask_config), (24 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(i.dma_fifo_status_get), (20 bytes).
    Removing gd32f4xx_dma.o(i.dma_flow_controller_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_disable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_enable), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_clear), (62 bytes).
    Removing gd32f4xx_dma.o(i.dma_interrupt_flag_get), (516 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_address_generation_config), (64 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_memory_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_mode_init), (356 bytes).
    Removing gd32f4xx_dma.o(i.dma_multi_data_para_struct_init), (40 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_address_config), (16 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_burst_beats_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_periph_width_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_peripheral_address_generation_config), (126 bytes).
    Removing gd32f4xx_dma.o(i.dma_priority_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_config), (76 bytes).
    Removing gd32f4xx_dma.o(i.dma_switch_buffer_mode_enable), (66 bytes).
    Removing gd32f4xx_dma.o(i.dma_transfer_direction_config), (36 bytes).
    Removing gd32f4xx_dma.o(i.dma_using_memory_get), (28 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_config), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_address_filter_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_current_desc_address_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_debug_status_get), (108 bytes).
    Removing gd32f4xx_enet.o(i.enet_default_init), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_deinit), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_delay), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_clear), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_get), (14 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_flag_set), (8 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_information_get), (100 bytes).
    Removing gd32f4xx_enet.o(i.enet_desc_select_normal_mode), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_chain_init), (200 bytes).
    Removing gd32f4xx_enet.o(i.enet_descriptors_ring_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_disable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dma_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_resume), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_dmaprocess_state_get), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_enable), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_fliter_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_feature_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_flowcontrol_threshold_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_disable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_forward_feature_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_receive), (248 bytes).
    Removing gd32f4xx_enet.o(i.enet_frame_transmit), (204 bytes).
    Removing gd32f4xx_enet.o(i.enet_init), (868 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_config), (356 bytes).
    Removing gd32f4xx_enet.o(i.enet_initpara_reset), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_disable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_enable), (72 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_clear), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_get), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_mac_address_set), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_missed_frame_counter_get), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_get), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_preset_config), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_counters_reset), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_disable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_msc_feature_enable), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_config), (44 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_detect_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_pauseframe_generate), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_config), (216 bytes).
    Removing gd32f4xx_enet.o(i.enet_phy_write_read), (156 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_disable), (50 bytes).
    Removing gd32f4xx_enet.o(i.enet_phyloopback_enable), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_expected_time_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_chain_init), (236 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_normal_descriptors_ring_init), (280 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_pps_output_frequency_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_subsecond_increment_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_system_time_get), (32 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_addend_config), (12 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_function_config), (256 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptp_timestamp_update_config), (24 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_receive_normal_mode), (340 bytes).
    Removing gd32f4xx_enet.o(i.enet_ptpframe_transmit_normal_mode), (444 bytes).
    Removing gd32f4xx_enet.o(i.enet_registers_get), (56 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_delay_receive_complete_interrupt), (20 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_desc_immediate_receive_complete_interrupt), (10 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_disable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rx_enable), (36 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_drop), (172 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxframe_size_get), (152 bytes).
    Removing gd32f4xx_enet.o(i.enet_rxprocess_check_recovery), (48 bytes).
    Removing gd32f4xx_enet.o(i.enet_software_reset), (60 bytes).
    Removing gd32f4xx_enet.o(i.enet_transmit_checksum_config), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_disable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_tx_enable), (40 bytes).
    Removing gd32f4xx_enet.o(i.enet_txfifo_flush), (52 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_disable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_feature_enable), (16 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_config), (28 bytes).
    Removing gd32f4xx_enet.o(i.enet_wum_filter_register_pointer_reset), (20 bytes).
    Removing gd32f4xx_enet.o(.bss), (15460 bytes).
    Removing gd32f4xx_enet.o(.constdata), (116 bytes).
    Removing gd32f4xx_enet.o(.data), (20 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_ecc_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_clear), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_flag_get), (52 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_disable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_enable), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_clear), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_interrupt_flag_get), (72 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_deinit), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_ecc_config), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_init), (172 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_nand_struct_para_init), (54 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_consecutive_clock_config), (42 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_disable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_enable), (22 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_init), (228 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_page_size_config), (40 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_norsram_struct_para_init), (106 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_deinit), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_disable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_enable), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_init), (188 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_pccard_struct_para_init), (60 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_autorefresh_number_set), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_bankstatus_get), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_command_config), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_deinit), (56 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_init), (284 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_config), (32 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_readsample_enable), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_refresh_count_set), (36 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_command_para_init), (16 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_struct_para_init), (66 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sdram_write_protection_config), (64 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_deinit), (44 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_high_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_init), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_low_id_get), (12 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_command_set), (28 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_read_id_command_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_send_command_state_get), (48 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_struct_para_init), (20 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_cmd_send), (24 bytes).
    Removing gd32f4xx_exmc.o(i.exmc_sqpipsram_write_command_set), (28 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(i.exti_deinit), (28 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_event_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_init), (188 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_enable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_exti.o(i.exti_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_disable), (16 bytes).
    Removing gd32f4xx_exti.o(i.exti_software_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank0_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_bank1_erase), (68 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_byte_program), (80 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_flag_get), (24 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_halfword_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_disable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_enable), (16 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_clear), (12 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_interrupt_flag_get), (64 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_mass_erase), (72 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_page_erase), (124 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_ready_wait), (32 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_sector_erase), (96 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_state_get), (76 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_word_program), (84 bytes).
    Removing gd32f4xx_fmc.o(i.fmc_wscnt_set), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_boot_mode_config), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_double_bank_select), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp0_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp1_get), (32 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_disable), (96 bytes).
    Removing gd32f4xx_fmc.o(i.ob_drp_enable), (84 bytes).
    Removing gd32f4xx_fmc.o(i.ob_erase), (76 bytes).
    Removing gd32f4xx_fmc.o(i.ob_lock), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_security_protection_config), (40 bytes).
    Removing gd32f4xx_fmc.o(i.ob_spc_get), (28 bytes).
    Removing gd32f4xx_fmc.o(i.ob_start), (20 bytes).
    Removing gd32f4xx_fmc.o(i.ob_unlock), (36 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold), (24 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_bor_threshold_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_user_write), (52 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection0_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection1_get), (16 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_disable), (72 bytes).
    Removing gd32f4xx_fmc.o(i.ob_write_protection_enable), (72 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_config), (104 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_counter_reload), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_enable), (16 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_prescaler_value_config), (60 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_reload_value_config), (64 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_disable), (12 bytes).
    Removing gd32f4xx_fwdgt.o(i.fwdgt_write_enable), (16 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_bit_toggle), (4 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_deinit), (206 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_input_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_bit_get), (16 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_output_port_get), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_pin_lock), (18 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_toggle), (8 bytes).
    Removing gd32f4xx_gpio.o(i.gpio_port_write), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_ackpos_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_analog_noise_filter_enable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_receive), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_data_transmit), (6 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_digital_noise_filter_config), (8 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dma_last_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_disable), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_dualaddr_enable), (12 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_disable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_enable), (26 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_clear), (44 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_interrupt_flag_get), (92 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_transfer_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_pec_value_get), (10 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_disable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_sam_timeout_enable), (14 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_slave_response_to_gcall_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_alert_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_arp_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_smbus_type_config), (24 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_software_reset_config), (16 bytes).
    Removing gd32f4xx_i2c.o(i.i2c_stretch_scl_low_config), (16 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_background_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_deinit), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_init), (316 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_destination_struct_para_init), (22 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_init), (164 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_init), (100 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_lut_loading_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_foreground_struct_para_init), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_inter_timer_config), (36 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_disable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_enable), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_interval_clock_num_config), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_line_mark_config), (24 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_pixel_format_convert_mode_set), (28 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_hangup_enable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_disable), (20 bytes).
    Removing gd32f4xx_ipa.o(i.ipa_transfer_stop_enable), (20 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(i.iref_deinit), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_disable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_enable), (20 bytes).
    Removing gd32f4xx_iref.o(i.iref_mode_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_precision_trim_value_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_sink_set), (28 bytes).
    Removing gd32f4xx_iref.o(i.iref_step_data_config), (28 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_misc.o(i.nvic_irq_disable), (24 bytes).
    Removing gd32f4xx_misc.o(i.nvic_vector_table_set), (24 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_reset), (16 bytes).
    Removing gd32f4xx_misc.o(i.system_lowpower_set), (16 bytes).
    Removing gd32f4xx_misc.o(i.systick_clksource_set), (40 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_ldo_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_backup_write_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_deinit), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_clear), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_flag_get), (24 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_highdriver_switch_select), (44 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_ldo_output_select), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowdriver_mode_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lowpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_lvd_select), (48 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_normalpower_driver_config), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_deepsleepmode), (244 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_sleepmode), (28 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_to_standbymode), (108 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_disable), (20 bytes).
    Removing gd32f4xx_pmu.o(i.pmu_wakeup_pin_enable), (20 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ahb_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb1_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_apb2_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_bkp_reset_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ck48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout0_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_ckout1_config), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deepsleep_voltage_set), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_deinit), (140 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_hxtal_clock_monitor_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_i2s_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_clear), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_irc16m_adjust_value_set), (28 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_lxtal_drive_capability_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_disable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_bypass_mode_enable), (116 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_osci_off), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_disable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_periph_clock_sleep_enable), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll48m_clock_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pll_config), (132 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_plli2s_config), (44 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_pllsai_config), (72 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_rtc_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_config), (32 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_disable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_spread_spectrum_enable), (20 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_system_clock_source_get), (16 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_timer_clock_prescaler_config), (36 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_tli_clock_div_config), (24 bytes).
    Removing gd32f4xx_rcu.o(i.rcu_voltage_key_unlock), (16 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_config), (100 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_disable), (128 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_get), (68 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_output_config), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_config), (52 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_alarm_subsecond_get), (32 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_bypass_shadow_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_calibration_output_config), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_config), (116 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_coarse_calibration_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_deinit), (204 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_clear), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_flag_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_hour_adjust), (36 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_disable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_interrupt_enable), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_disable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_refclock_detection_enable), (56 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_second_adjust), (108 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_smooth_calibration_config), (80 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_subsecond_get), (20 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper0_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_disable), (16 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_tamper_enable), (200 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_disable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_enable), (48 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_get), (60 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_pin_map), (28 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_timestamp_subsecond_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_clock_set), (92 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_disable), (84 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_enable), (40 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_get), (12 bytes).
    Removing gd32f4xx_rtc.o(i.rtc_wakeup_timer_set), (76 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_completion_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_command_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_ceata_interrupt_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_clock_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_csm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_data_write), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dma_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_dsm_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_fifo_counter_get), (12 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_hardware_clock_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_interrupt_enable), (16 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_operation_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_readwait_type_set), (40 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_stop_readwait_enable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_disable), (20 bytes).
    Removing gd32f4xx_sdio.o(i.sdio_suspend_enable), (20 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(i.i2s_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.i2s_full_duplex_mode_config), (48 bytes).
    Removing gd32f4xx_spi.o(i.i2s_init), (28 bytes).
    Removing gd32f4xx_spi.o(i.i2s_psc_config), (292 bytes).
    Removing gd32f4xx_spi.o(i.spi_bidirectional_transfer_config), (26 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_error_clear), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_next), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_off), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_on), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_get), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_crc_polynomial_set), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_disable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_dma_enable), (22 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_frame_format_config), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_receive), (8 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_data_transmit), (4 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_deinit), (172 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_flag_get), (16 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_format_error_clear), (6 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_disable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_enable), (48 bytes).
    Removing gd32f4xx_spi.o(i.spi_i2s_interrupt_flag_get), (112 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_high), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_internal_low), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_nss_output_enable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_disable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_io23_output_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_read_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_quad_write_enable), (14 bytes).
    Removing gd32f4xx_spi.o(i.spi_struct_para_init), (18 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_disable), (10 bytes).
    Removing gd32f4xx_spi.o(i.spi_ti_mode_enable), (10 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_bootmode_config), (28 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_compensation_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_deinit), (20 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_enet_phy_interface_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exmc_swap_config), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_exti_line_config), (172 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_flag_get), (24 bytes).
    Removing gd32f4xx_syscfg.o(i.syscfg_fmc_swap_config), (24 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_auto_reload_shadow_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_automatic_output_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_autoreload_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_break_struct_para_init), (18 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_capture_value_register_read), (42 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_polarity_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_complementary_output_state_config), (70 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_control_shadow_update_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_dma_request_source_select), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_capture_prescaler_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_input_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_clear_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_config), (492 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_fast_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_mode_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_polarity_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_pulse_value_config), (38 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_shadow_config), (90 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_state_config), (92 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_output_struct_para_init), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_channel_remap_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_alignment), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_down_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_read), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_up_direction), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_counter_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_deinit), (388 bytes).
    Removing gd32f4xx_timer.o(i.timer_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_dma_transfer_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_event_software_generate), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode0_config), (40 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_clock_mode1_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_as_external_clock_config), (166 bytes).
    Removing gd32f4xx_timer.o(i.timer_external_trigger_config), (30 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_flag_get), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_hall_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_init), (152 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_capture_config), (326 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_pwm_capture_config), (356 bytes).
    Removing gd32f4xx_timer.o(i.timer_input_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_clock_config), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_internal_trigger_as_external_clock_config), (32 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_disable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_enable), (8 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_clear), (6 bytes).
    Removing gd32f4xx_timer.o(i.timer_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_output_trigger_source_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_master_slave_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_output_value_selection_config), (34 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_config), (14 bytes).
    Removing gd32f4xx_timer.o(i.timer_prescaler_read), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_primary_output_config), (24 bytes).
    Removing gd32f4xx_timer.o(i.timer_quadrature_decoder_mode_config), (64 bytes).
    Removing gd32f4xx_timer.o(i.timer_repetition_value_config), (4 bytes).
    Removing gd32f4xx_timer.o(i.timer_single_pulse_mode_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_slave_mode_select), (16 bytes).
    Removing gd32f4xx_timer.o(i.timer_struct_para_init), (22 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_disable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_event_enable), (10 bytes).
    Removing gd32f4xx_timer.o(i.timer_update_source_config), (26 bytes).
    Removing gd32f4xx_timer.o(i.timer_write_chxval_register_config), (34 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_color_key_init), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_current_pos_get), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_deinit), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_disable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_dither_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_enable), (20 bytes).
    Removing gd32f4xx_tli.o(i.tli_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_init), (188 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_disable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_enable), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_tli.o(i.tli_interrupt_flag_get), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_init), (152 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_struct_para_init), (48 bytes).
    Removing gd32f4xx_tli.o(i.tli_layer_window_offset_modify), (228 bytes).
    Removing gd32f4xx_tli.o(i.tli_line_mark_set), (24 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_disable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_enable), (14 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_init), (28 bytes).
    Removing gd32f4xx_tli.o(i.tli_lut_struct_para_init), (12 bytes).
    Removing gd32f4xx_tli.o(i.tli_reload_config), (36 bytes).
    Removing gd32f4xx_tli.o(i.tli_struct_para_init), (34 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(i.trng_deinit), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_flag_get), (24 bytes).
    Removing gd32f4xx_trng.o(i.trng_get_true_random_data), (12 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_disable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_enable), (20 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_clear), (16 bytes).
    Removing gd32f4xx_trng.o(i.trng_interrupt_flag_get), (24 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_usart.o(i.usart_address_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_block_length_config), (28 bytes).
    Removing gd32f4xx_usart.o(i.usart_break_frame_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_data_first_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_dma_transmit_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_flag_clear), (52 bytes).
    Removing gd32f4xx_usart.o(i.usart_guard_time_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_halfduplex_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_cts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_hardware_flow_rts_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_invert_config), (104 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_lowpower_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_irda_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_break_detection_length_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_lin_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_mute_mode_wakeup_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_oversample_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_parity_check_coherence_config), (24 bytes).
    Removing gd32f4xx_usart.o(i.usart_prescaler_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_disable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_enable), (14 bytes).
    Removing gd32f4xx_usart.o(i.usart_receiver_timeout_threshold_config), (20 bytes).
    Removing gd32f4xx_usart.o(i.usart_sample_bit_config), (16 bytes).
    Removing gd32f4xx_usart.o(i.usart_send_break), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_autoretry_config), (26 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_smartcard_mode_nack_enable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_config), (34 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_disable), (10 bytes).
    Removing gd32f4xx_usart.o(i.usart_synchronous_clock_enable), (10 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_config), (28 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_counter_update), (16 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_deinit), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_enable), (20 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_clear), (12 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_flag_get), (24 bytes).
    Removing gd32f4xx_wwdgt.o(i.wwdgt_interrupt_enable), (20 bytes).

958 unused section(s) (total 74163 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\Compenents\Btn\Src\ebtn.c             0x00000000   Number         0  ebtn.o ABSOLUTE
    ..\Compenents\Fatfs\Src\diskio.c         0x00000000   Number         0  diskio.o ABSOLUTE
    ..\Compenents\Fatfs\Src\ff.c             0x00000000   Number         0  ff.o ABSOLUTE
    ..\Compenents\Fatfs\Src\unicode.c        0x00000000   Number         0  unicode.o ABSOLUTE
    ..\Compenents\Gd25qxx\Src\gd25qxx.c      0x00000000   Number         0  gd25qxx.o ABSOLUTE
    ..\Compenents\Oled\Src\oled.c            0x00000000   Number         0  oled.o ABSOLUTE
    ..\Compenents\Sdio\Src\sdio_sdcard.c     0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\Core\Src\gd32f470vet6_bsp.c           0x00000000   Number         0  gd32f470vet6_bsp.o ABSOLUTE
    ..\Core\Src\gd32f4xx_it.c                0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\scheduler.c                  0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\Core\Src\systick.c                    0x00000000   Number         0  systick.o ABSOLUTE
    ..\Firmware\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s 0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    ..\Firmware\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\Firmware\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\MyApps\Src\adc_app.c                  0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\MyApps\Src\btn_app.c                  0x00000000   Number         0  btn_app.o ABSOLUTE
    ..\MyApps\Src\led_app.c                  0x00000000   Number         0  led_app.o ABSOLUTE
    ..\MyApps\Src\oled_app.c                 0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\MyApps\Src\rtc_app.c                  0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\MyApps\Src\tf_app.c                   0x00000000   Number         0  tf_app.o ABSOLUTE
    ..\MyApps\Src\usart_app.c                0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\\Compenents\\Fatfs\\Src\\diskio.c     0x00000000   Number         0  diskio.o ABSOLUTE
    ..\\Compenents\\Gd25qxx\\Src\\gd25qxx.c  0x00000000   Number         0  gd25qxx.o ABSOLUTE
    ..\\Compenents\\Oled\\Src\\oled.c        0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Compenents\\Sdio\\Src\\sdio_sdcard.c 0x00000000   Number         0  sdio_sdcard.o ABSOLUTE
    ..\\Core\\Src\\gd32f470vet6_bsp.c        0x00000000   Number         0  gd32f470vet6_bsp.o ABSOLUTE
    ..\\Core\\Src\\gd32f4xx_it.c             0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\\Core\\Src\\main.c                    0x00000000   Number         0  main.o ABSOLUTE
    ..\\Core\\Src\\scheduler.c               0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\Core\\Src\\systick.c                 0x00000000   Number         0  systick.o ABSOLUTE
    ..\\Firmware\\CMSIS\\GD\\GD32F4xx\\Source\\system_gd32f4xx.c 0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\\Firmware\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\\MyApps\\Src\\adc_app.c               0x00000000   Number         0  adc_app.o ABSOLUTE
    ..\\MyApps\\Src\\btn_app.c               0x00000000   Number         0  btn_app.o ABSOLUTE
    ..\\MyApps\\Src\\led_app.c               0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\MyApps\\Src\\oled_app.c              0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\MyApps\\Src\\rtc_app.c               0x00000000   Number         0  rtc_app.o ABSOLUTE
    ..\\MyApps\\Src\\tf_app.c                0x00000000   Number         0  tf_app.o ABSOLUTE
    ..\\MyApps\\Src\\usart_app.c             0x00000000   Number         0  usart_app.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    !!!main                                  0x080001ac   Section        8  __main.o(!!!main)
    !!!scatter                               0x080001b4   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001e8   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000244   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000260   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x08000260   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000266   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x0800026c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x08000272   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000278   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800027e   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000284   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800028e   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000294   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x0800029a   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080002a0   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080002a6   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080002ac   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080002b2   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080002b8   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x080002be   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080002c4   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080002ca   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080002d4   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002da   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002e0   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002e6   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002ec   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002f0   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002f2   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080002f6   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002fc   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080002fc   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000308   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000308   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000308   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000312   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000312   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000314   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000316   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000316   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000318   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000318   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000318   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800031e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800031e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000322   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000322   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800032a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800032c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800032c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000330   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000338   Section       64  startup_gd32f450_470.o(.text)
    $v0                                      0x08000338   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x08000378   Section        0  vsnprintf.o(.text)
    .text                                    0x080003ac   Section        0  memcmp.o(.text)
    .text                                    0x08000404   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x0800048e   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x080004f2   Section       68  rt_memclr.o(.text)
    .text                                    0x08000536   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000584   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000604   Section        0  heapauxi.o(.text)
    .text                                    0x0800060a   Section        0  _printf_pad.o(.text)
    .text                                    0x08000658   Section        0  _printf_truncate.o(.text)
    .text                                    0x0800067c   Section        0  _printf_str.o(.text)
    .text                                    0x080006d0   Section        0  _printf_dec.o(.text)
    .text                                    0x08000748   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000770   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000771   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080007a0   Section        0  _sputc.o(.text)
    .text                                    0x080007aa   Section        0  _snputc.o(.text)
    .text                                    0x080007bc   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000878   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x080008f4   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x080008f5   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000964   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000965   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x080009f8   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000b80   Section      138  lludiv10.o(.text)
    .text                                    0x08000c0a   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000cbc   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000cbf   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x080010dc   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x080013d8   Section        0  _printf_char.o(.text)
    .text                                    0x08001404   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001430   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001470   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080014bc   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080014cc   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080014d4   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001554   Section        0  bigflt0.o(.text)
    .text                                    0x08001638   Section        0  exit.o(.text)
    .text                                    0x0800164c   Section        8  libspace.o(.text)
    .text                                    0x08001654   Section        0  sys_exit.o(.text)
    .text                                    0x08001660   Section        2  use_no_semi.o(.text)
    .text                                    0x08001662   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x08001662   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080016a0   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x080016e6   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001746   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001a7e   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001b5a   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001b84   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001bae   Section      580  btod.o(CL$$btod_mult_common)
    i.AdcDmaInit                             0x08001df4   Section        0  gd32f470vet6_bsp.o(i.AdcDmaInit)
    AdcDmaInit                               0x08001df5   Thumb Code    94  gd32f470vet6_bsp.o(i.AdcDmaInit)
    i.AdcPeriphInit                          0x08001e60   Section        0  gd32f470vet6_bsp.o(i.AdcPeriphInit)
    i.AdcTask                                0x08001f08   Section        0  adc_app.o(i.AdcTask)
    i.BcdToDec                               0x08001f5c   Section        0  gd32f470vet6_bsp.o(i.BcdToDec)
    i.BtnEventCallback                       0x08001f70   Section        0  btn_app.o(i.BtnEventCallback)
    i.BtnGetState                            0x08002040   Section        0  gd32f470vet6_bsp.o(i.BtnGetState)
    BtnGetState                              0x08002041   Thumb Code   156  gd32f470vet6_bsp.o(i.BtnGetState)
    i.BtnPeriphInit                          0x080020e4   Section        0  gd32f470vet6_bsp.o(i.BtnPeriphInit)
    i.BtnTask                                0x0800215c   Section        0  btn_app.o(i.BtnTask)
    i.BusFault_Handler                       0x0800216c   Section        0  gd32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08002170   Section        0  gd32f4xx_it.o(i.DebugMon_Handler)
    i.DecToBcd                               0x08002174   Section        0  gd32f470vet6_bsp.o(i.DecToBcd)
    i.FlashPeriphInit                        0x08002190   Section        0  gd32f470vet6_bsp.o(i.FlashPeriphInit)
    i.HardFault_Handler                      0x08002228   Section        0  gd32f4xx_it.o(i.HardFault_Handler)
    i.I2C_Bus_Reset                          0x0800222c   Section        0  oled.o(i.I2C_Bus_Reset)
    I2C_Bus_Reset                            0x0800222d   Thumb Code   282  oled.o(i.I2C_Bus_Reset)
    i.LedDisp                                0x08002354   Section        0  gd32f470vet6_bsp.o(i.LedDisp)
    i.LedPeriphInit                          0x080023dc   Section        0  gd32f470vet6_bsp.o(i.LedPeriphInit)
    i.LedTask                                0x08002414   Section        0  led_app.o(i.LedTask)
    i.MemManage_Handler                      0x08002428   Section        0  gd32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800242c   Section        0  gd32f4xx_it.o(i.NMI_Handler)
    i.NVIC_SetPriority                       0x08002430   Section        0  systick.o(i.NVIC_SetPriority)
    NVIC_SetPriority                         0x08002431   Thumb Code    32  systick.o(i.NVIC_SetPriority)
    i.OLED_Clear                             0x08002458   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08002490   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x080024c0   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_Write_cmd                         0x080024e4   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x0800260c   Section        0  oled.o(i.OLED_Write_data)
    i.OledDmaInit                            0x08002734   Section        0  gd32f470vet6_bsp.o(i.OledDmaInit)
    OledDmaInit                              0x08002735   Thumb Code    90  gd32f470vet6_bsp.o(i.OledDmaInit)
    i.OledPeriphInit                         0x0800279c   Section        0  gd32f470vet6_bsp.o(i.OledPeriphInit)
    i.PendSV_Handler                         0x0800283c   Section        0  gd32f4xx_it.o(i.PendSV_Handler)
    i.ReadRtc                                0x08002840   Section        0  gd32f470vet6_bsp.o(i.ReadRtc)
    i.RtcPeriphInit                          0x080028ac   Section        0  gd32f470vet6_bsp.o(i.RtcPeriphInit)
    i.RtcPreConfig                           0x080029d0   Section        0  gd32f470vet6_bsp.o(i.RtcPreConfig)
    RtcPreConfig                             0x080029d1   Thumb Code    54  gd32f470vet6_bsp.o(i.RtcPreConfig)
    i.RtcTask                                0x08002a10   Section        0  rtc_app.o(i.RtcTask)
    i.SDIO_IRQHandler                        0x08002a80   Section        0  gd32f4xx_it.o(i.SDIO_IRQHandler)
    i.SVC_Handler                            0x08002a88   Section        0  gd32f4xx_it.o(i.SVC_Handler)
    i.SetRtc                                 0x08002a8c   Section        0  gd32f470vet6_bsp.o(i.SetRtc)
    i.SysInit                                0x08002b30   Section        0  gd32f470vet6_bsp.o(i.SysInit)
    i.SysTick_Handler                        0x08002b58   Section        0  gd32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08002b70   Section        0  system_gd32f4xx.o(i.SystemInit)
    i.TaskExeution                           0x08002cec   Section        0  scheduler.o(i.TaskExeution)
    i.TfPeriphInit                           0x08002d48   Section        0  gd32f470vet6_bsp.o(i.TfPeriphInit)
    i.USART0_IRQHandler                      0x08002dd0   Section        0  gd32f4xx_it.o(i.USART0_IRQHandler)
    i.USART1_IRQHandler                      0x08002e74   Section        0  main.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08002ec0   Section        0  gd32f4xx_it.o(i.UsageFault_Handler)
    i.Usart0DmaInit                          0x08002ec4   Section        0  gd32f470vet6_bsp.o(i.Usart0DmaInit)
    Usart0DmaInit                            0x08002ec5   Thumb Code    96  gd32f470vet6_bsp.o(i.Usart0DmaInit)
    i.Usart0PeriphInit                       0x08002f30   Section        0  gd32f470vet6_bsp.o(i.Usart0PeriphInit)
    i.Usart0Printf                           0x08002fe4   Section        0  gd32f470vet6_bsp.o(i.Usart0Printf)
    i.Usart0Task                             0x08003054   Section        0  usart_app.o(i.Usart0Task)
    i.__ARM_fpclassify                       0x0800308c   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x080030bc   Section        0  __printf_wp.o(i._is_digit)
    i._soft_delay_                           0x080030ca   Section        0  system_gd32f4xx.o(i._soft_delay_)
    _soft_delay_                             0x080030cb   Thumb Code    28  system_gd32f4xx.o(i._soft_delay_)
    i.adc_calibration_enable                 0x080030e6   Section        0  gd32f4xx_adc.o(i.adc_calibration_enable)
    i.adc_channel_length_config              0x08003110   Section        0  gd32f4xx_adc.o(i.adc_channel_length_config)
    i.adc_clock_config                       0x08003164   Section        0  gd32f4xx_adc.o(i.adc_clock_config)
    i.adc_data_alignment_config              0x08003188   Section        0  gd32f4xx_adc.o(i.adc_data_alignment_config)
    i.adc_dma_mode_enable                    0x0800319e   Section        0  gd32f4xx_adc.o(i.adc_dma_mode_enable)
    i.adc_dma_request_after_last_enable      0x080031a8   Section        0  gd32f4xx_adc.o(i.adc_dma_request_after_last_enable)
    i.adc_enable                             0x080031b2   Section        0  gd32f4xx_adc.o(i.adc_enable)
    i.adc_external_trigger_config            0x080031c4   Section        0  gd32f4xx_adc.o(i.adc_external_trigger_config)
    i.adc_external_trigger_source_config     0x080031f8   Section        0  gd32f4xx_adc.o(i.adc_external_trigger_source_config)
    i.adc_routine_channel_config             0x08003228   Section        0  gd32f4xx_adc.o(i.adc_routine_channel_config)
    i.adc_software_trigger_enable            0x080032d4   Section        0  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    i.adc_special_function_config            0x080032f8   Section        0  gd32f4xx_adc.o(i.adc_special_function_config)
    i.adc_sync_mode_config                   0x08003354   Section        0  gd32f4xx_adc.o(i.adc_sync_mode_config)
    i.bit_array_and                          0x08003378   Section        0  ebtn.o(i.bit_array_and)
    bit_array_and                            0x08003379   Thumb Code    38  ebtn.o(i.bit_array_and)
    i.bit_array_assign                       0x0800339e   Section        0  ebtn.o(i.bit_array_assign)
    bit_array_assign                         0x0800339f   Thumb Code    46  ebtn.o(i.bit_array_assign)
    i.bit_array_cmp                          0x080033cc   Section        0  ebtn.o(i.bit_array_cmp)
    bit_array_cmp                            0x080033cd   Thumb Code    36  ebtn.o(i.bit_array_cmp)
    i.bit_array_get                          0x080033f0   Section        0  ebtn.o(i.bit_array_get)
    bit_array_get                            0x080033f1   Thumb Code    22  ebtn.o(i.bit_array_get)
    i.bit_array_num_bits_set                 0x08003406   Section        0  ebtn.o(i.bit_array_num_bits_set)
    bit_array_num_bits_set                   0x08003407   Thumb Code    84  ebtn.o(i.bit_array_num_bits_set)
    i.bit_array_or                           0x0800345a   Section        0  ebtn.o(i.bit_array_or)
    bit_array_or                             0x0800345b   Thumb Code    38  ebtn.o(i.bit_array_or)
    i.cmdsent_error_check                    0x08003480   Section        0  sdio_sdcard.o(i.cmdsent_error_check)
    cmdsent_error_check                      0x08003481   Thumb Code    40  sdio_sdcard.o(i.cmdsent_error_check)
    i.delay_1ms                              0x080034b0   Section        0  systick.o(i.delay_1ms)
    i.delay_decrement                        0x080034c4   Section        0  systick.o(i.delay_decrement)
    i.disk_initialize                        0x080034dc   Section        0  diskio.o(i.disk_initialize)
    i.dma_channel_disable                    0x08003562   Section        0  gd32f4xx_dma.o(i.dma_channel_disable)
    i.dma_channel_enable                     0x08003582   Section        0  gd32f4xx_dma.o(i.dma_channel_enable)
    i.dma_channel_subperipheral_select       0x080035a2   Section        0  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    i.dma_circulation_disable                0x080035c8   Section        0  gd32f4xx_dma.o(i.dma_circulation_disable)
    i.dma_circulation_enable                 0x080035e8   Section        0  gd32f4xx_dma.o(i.dma_circulation_enable)
    i.dma_deinit                             0x08003608   Section        0  gd32f4xx_dma.o(i.dma_deinit)
    i.dma_flag_clear                         0x080036ae   Section        0  gd32f4xx_dma.o(i.dma_flag_clear)
    i.dma_flag_get                           0x080036ec   Section        0  gd32f4xx_dma.o(i.dma_flag_get)
    i.dma_memory_address_config              0x08003738   Section        0  gd32f4xx_dma.o(i.dma_memory_address_config)
    i.dma_single_data_mode_init              0x08003758   Section        0  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    i.dma_single_data_para_struct_init       0x080038b0   Section        0  gd32f4xx_dma.o(i.dma_single_data_para_struct_init)
    i.dma_transfer_number_config             0x080038d2   Section        0  gd32f4xx_dma.o(i.dma_transfer_number_config)
    i.dma_transfer_number_get                0x080038e2   Section        0  gd32f4xx_dma.o(i.dma_transfer_number_get)
    i.ebtn_combo_btn_add_btn                 0x080038f2   Section        0  ebtn.o(i.ebtn_combo_btn_add_btn)
    i.ebtn_combo_btn_add_btn_by_idx          0x08003912   Section        0  ebtn.o(i.ebtn_combo_btn_add_btn_by_idx)
    i.ebtn_get_btn_index_by_key_id           0x08003934   Section        0  ebtn.o(i.ebtn_get_btn_index_by_key_id)
    i.ebtn_get_current_state                 0x0800397c   Section        0  ebtn.o(i.ebtn_get_current_state)
    ebtn_get_current_state                   0x0800397d   Thumb Code    82  ebtn.o(i.ebtn_get_current_state)
    i.ebtn_init                              0x080039d4   Section        0  ebtn.o(i.ebtn_init)
    i.ebtn_process                           0x08003a30   Section        0  ebtn.o(i.ebtn_process)
    i.ebtn_process_btn                       0x08003a4a   Section        0  ebtn.o(i.ebtn_process_btn)
    ebtn_process_btn                         0x08003a4b   Thumb Code    62  ebtn.o(i.ebtn_process_btn)
    i.ebtn_process_btn_combo                 0x08003a88   Section        0  ebtn.o(i.ebtn_process_btn_combo)
    ebtn_process_btn_combo                   0x08003a89   Thumb Code   124  ebtn.o(i.ebtn_process_btn_combo)
    i.ebtn_process_with_curr_state           0x08003b04   Section        0  ebtn.o(i.ebtn_process_with_curr_state)
    i.ebtn_set_combo_suppress_threshold      0x08003cc4   Section        0  ebtn.o(i.ebtn_set_combo_suppress_threshold)
    i.ebtn_set_config                        0x08003cd0   Section        0  ebtn.o(i.ebtn_set_config)
    i.ebtn_timer_sub                         0x08003cdc   Section        0  ebtn.o(i.ebtn_timer_sub)
    ebtn_timer_sub                           0x08003cdd   Thumb Code     6  ebtn.o(i.ebtn_timer_sub)
    i.f_mount                                0x08003ce4   Section        0  ff.o(i.f_mount)
    i.gpio_af_set                            0x08003d10   Section        0  gd32f4xx_gpio.o(i.gpio_af_set)
    i.gpio_bit_reset                         0x08003d6e   Section        0  gd32f4xx_gpio.o(i.gpio_bit_reset)
    i.gpio_bit_set                           0x08003d72   Section        0  gd32f4xx_gpio.o(i.gpio_bit_set)
    i.gpio_bit_write                         0x08003d76   Section        0  gd32f4xx_gpio.o(i.gpio_bit_write)
    i.gpio_config                            0x08003d80   Section        0  sdio_sdcard.o(i.gpio_config)
    gpio_config                              0x08003d81   Thumb Code   106  sdio_sdcard.o(i.gpio_config)
    i.gpio_input_bit_get                     0x08003df4   Section        0  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    i.gpio_mode_set                          0x08003e04   Section        0  gd32f4xx_gpio.o(i.gpio_mode_set)
    i.gpio_output_options_set                0x08003e52   Section        0  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i.i2c_ack_config                         0x08003e94   Section        0  gd32f4xx_i2c.o(i.i2c_ack_config)
    i.i2c_clock_config                       0x08003ea4   Section        0  gd32f4xx_i2c.o(i.i2c_clock_config)
    i.i2c_deinit                             0x08003f88   Section        0  gd32f4xx_i2c.o(i.i2c_deinit)
    i.i2c_dma_config                         0x08003fe0   Section        0  gd32f4xx_i2c.o(i.i2c_dma_config)
    i.i2c_enable                             0x08003ff0   Section        0  gd32f4xx_i2c.o(i.i2c_enable)
    i.i2c_flag_clear                         0x08003ffa   Section        0  gd32f4xx_i2c.o(i.i2c_flag_clear)
    i.i2c_flag_get                           0x08004022   Section        0  gd32f4xx_i2c.o(i.i2c_flag_get)
    i.i2c_master_addressing                  0x08004040   Section        0  gd32f4xx_i2c.o(i.i2c_master_addressing)
    i.i2c_mode_addr_config                   0x08004054   Section        0  gd32f4xx_i2c.o(i.i2c_mode_addr_config)
    i.i2c_start_on_bus                       0x08004070   Section        0  gd32f4xx_i2c.o(i.i2c_start_on_bus)
    i.i2c_stop_on_bus                        0x0800407a   Section        0  gd32f4xx_i2c.o(i.i2c_stop_on_bus)
    i.main                                   0x08004084   Section        0  main.o(i.main)
    i.nvic_irq_enable                        0x08004100   Section        0  gd32f4xx_misc.o(i.nvic_irq_enable)
    i.nvic_priority_group_set                0x080041c4   Section        0  gd32f4xx_misc.o(i.nvic_priority_group_set)
    i.pmu_backup_write_enable                0x080041d8   Section        0  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    i.prv_get_combo_btn_by_key_id            0x080041ec   Section        0  ebtn.o(i.prv_get_combo_btn_by_key_id)
    prv_get_combo_btn_by_key_id              0x080041ed   Thumb Code    70  ebtn.o(i.prv_get_combo_btn_by_key_id)
    i.prv_process_btn                        0x08004238   Section        0  ebtn.o(i.prv_process_btn)
    prv_process_btn                          0x08004239   Thumb Code   878  ebtn.o(i.prv_process_btn)
    i.r1_error_check                         0x080045ac   Section        0  sdio_sdcard.o(i.r1_error_check)
    r1_error_check                           0x080045ad   Thumb Code   120  sdio_sdcard.o(i.r1_error_check)
    i.r1_error_type_check                    0x08004630   Section        0  sdio_sdcard.o(i.r1_error_type_check)
    r1_error_type_check                      0x08004631   Thumb Code   174  sdio_sdcard.o(i.r1_error_type_check)
    i.r2_error_check                         0x080046e0   Section        0  sdio_sdcard.o(i.r2_error_check)
    r2_error_check                           0x080046e1   Thumb Code    70  sdio_sdcard.o(i.r2_error_check)
    i.r3_error_check                         0x08004730   Section        0  sdio_sdcard.o(i.r3_error_check)
    r3_error_check                           0x08004731   Thumb Code    52  sdio_sdcard.o(i.r3_error_check)
    i.r6_error_check                         0x0800476c   Section        0  sdio_sdcard.o(i.r6_error_check)
    r6_error_check                           0x0800476d   Thumb Code   158  sdio_sdcard.o(i.r6_error_check)
    i.r7_error_check                         0x08004814   Section        0  sdio_sdcard.o(i.r7_error_check)
    r7_error_check                           0x08004815   Thumb Code    74  sdio_sdcard.o(i.r7_error_check)
    i.rcu_all_reset_flag_clear               0x08004864   Section        0  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    i.rcu_clock_freq_get                     0x08004878   Section        0  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    i.rcu_config                             0x0800499c   Section        0  sdio_sdcard.o(i.rcu_config)
    rcu_config                               0x0800499d   Thumb Code    36  sdio_sdcard.o(i.rcu_config)
    i.rcu_flag_get                           0x080049c0   Section        0  gd32f4xx_rcu.o(i.rcu_flag_get)
    i.rcu_osci_on                            0x080049e4   Section        0  gd32f4xx_rcu.o(i.rcu_osci_on)
    i.rcu_osci_stab_wait                     0x08004a08   Section        0  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    i.rcu_periph_clock_enable                0x08004b64   Section        0  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    i.rcu_periph_reset_disable               0x08004b88   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    i.rcu_periph_reset_enable                0x08004bac   Section        0  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    i.rcu_rtc_clock_config                   0x08004bd0   Section        0  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    i.rs485_receive_enable                   0x08004be8   Section        0  main.o(i.rs485_receive_enable)
    i.rs485_send_enable                      0x08004bf4   Section        0  main.o(i.rs485_send_enable)
    i.rtc_current_time_get                   0x08004c0c   Section        0  gd32f4xx_rtc.o(i.rtc_current_time_get)
    i.rtc_init                               0x08004c70   Section        0  gd32f4xx_rtc.o(i.rtc_init)
    i.rtc_init_mode_enter                    0x08004d34   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    i.rtc_init_mode_exit                     0x08004d7c   Section        0  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    i.rtc_register_sync_wait                 0x08004d90   Section        0  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    i.sd_bus_mode_config                     0x08004df0   Section        0  sdio_sdcard.o(i.sd_bus_mode_config)
    i.sd_bus_width_config                    0x08004e84   Section        0  sdio_sdcard.o(i.sd_bus_width_config)
    sd_bus_width_config                      0x08004e85   Thumb Code   242  sdio_sdcard.o(i.sd_bus_width_config)
    i.sd_card_information_get                0x08004f80   Section        0  sdio_sdcard.o(i.sd_card_information_get)
    i.sd_card_init                           0x08005240   Section        0  sdio_sdcard.o(i.sd_card_init)
    i.sd_card_select_deselect                0x0800535c   Section        0  sdio_sdcard.o(i.sd_card_select_deselect)
    i.sd_cardstatus_get                      0x08005384   Section        0  sdio_sdcard.o(i.sd_cardstatus_get)
    i.sd_init                                0x080053cc   Section        0  sdio_sdcard.o(i.sd_init)
    i.sd_interrupts_process                  0x08005414   Section        0  sdio_sdcard.o(i.sd_interrupts_process)
    i.sd_power_on                            0x08005544   Section        0  sdio_sdcard.o(i.sd_power_on)
    i.sd_scr_get                             0x08005670   Section        0  sdio_sdcard.o(i.sd_scr_get)
    sd_scr_get                               0x08005671   Thumb Code   344  sdio_sdcard.o(i.sd_scr_get)
    i.sd_transfer_mode_config                0x080057cc   Section        0  sdio_sdcard.o(i.sd_transfer_mode_config)
    i.sd_transfer_stop                       0x080057e4   Section        0  sdio_sdcard.o(i.sd_transfer_stop)
    i.sdio_bus_mode_set                      0x08005808   Section        0  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    i.sdio_clock_config                      0x08005824   Section        0  gd32f4xx_sdio.o(i.sdio_clock_config)
    i.sdio_clock_enable                      0x08005858   Section        0  gd32f4xx_sdio.o(i.sdio_clock_enable)
    i.sdio_command_index_get                 0x0800586c   Section        0  gd32f4xx_sdio.o(i.sdio_command_index_get)
    i.sdio_command_response_config           0x08005878   Section        0  gd32f4xx_sdio.o(i.sdio_command_response_config)
    i.sdio_csm_enable                        0x080058b0   Section        0  gd32f4xx_sdio.o(i.sdio_csm_enable)
    i.sdio_data_config                       0x080058c4   Section        0  gd32f4xx_sdio.o(i.sdio_data_config)
    i.sdio_data_read                         0x08005900   Section        0  gd32f4xx_sdio.o(i.sdio_data_read)
    i.sdio_data_transfer_config              0x0800590c   Section        0  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    i.sdio_deinit                            0x08005928   Section        0  gd32f4xx_sdio.o(i.sdio_deinit)
    i.sdio_dsm_enable                        0x0800593c   Section        0  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    i.sdio_flag_clear                        0x08005950   Section        0  gd32f4xx_sdio.o(i.sdio_flag_clear)
    i.sdio_flag_get                          0x0800595c   Section        0  gd32f4xx_sdio.o(i.sdio_flag_get)
    i.sdio_hardware_clock_disable            0x08005970   Section        0  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    i.sdio_interrupt_disable                 0x08005984   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    i.sdio_interrupt_flag_clear              0x08005994   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    i.sdio_interrupt_flag_get                0x080059a0   Section        0  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    i.sdio_power_state_get                   0x080059b4   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_get)
    i.sdio_power_state_set                   0x080059c0   Section        0  gd32f4xx_sdio.o(i.sdio_power_state_set)
    i.sdio_response_get                      0x080059cc   Section        0  gd32f4xx_sdio.o(i.sdio_response_get)
    i.sdio_wait_type_set                     0x08005a08   Section        0  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    i.spi_enable                             0x08005a24   Section        0  gd32f4xx_spi.o(i.spi_enable)
    i.spi_flash_init                         0x08005a30   Section        0  gd25qxx.o(i.spi_flash_init)
    i.spi_init                               0x08005a4c   Section        0  gd32f4xx_spi.o(i.spi_init)
    i.system_clock_240m_25m_hxtal            0x08005a80   Section        0  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    system_clock_240m_25m_hxtal              0x08005a81   Thumb Code   258  system_gd32f4xx.o(i.system_clock_240m_25m_hxtal)
    i.system_clock_config                    0x08005b90   Section        0  system_gd32f4xx.o(i.system_clock_config)
    system_clock_config                      0x08005b91   Thumb Code     8  system_gd32f4xx.o(i.system_clock_config)
    i.systick_config                         0x08005b98   Section        0  systick.o(i.systick_config)
    i.usart1_enable_receive_interrupt        0x08005be8   Section        0  main.o(i.usart1_enable_receive_interrupt)
    i.usart1_rs485_init                      0x08005c04   Section        0  main.o(i.usart1_rs485_init)
    i.usart1_send_byte                       0x08005cc0   Section        0  main.o(i.usart1_send_byte)
    i.usart1_send_string                     0x08005ce0   Section        0  main.o(i.usart1_send_string)
    i.usart_baudrate_set                     0x08005d14   Section        0  gd32f4xx_usart.o(i.usart_baudrate_set)
    i.usart_data_receive                     0x08005dfc   Section        0  gd32f4xx_usart.o(i.usart_data_receive)
    i.usart_data_transmit                    0x08005e06   Section        0  gd32f4xx_usart.o(i.usart_data_transmit)
    i.usart_deinit                           0x08005e10   Section        0  gd32f4xx_usart.o(i.usart_deinit)
    i.usart_dma_receive_config               0x08005eec   Section        0  gd32f4xx_usart.o(i.usart_dma_receive_config)
    i.usart_enable                           0x08005f00   Section        0  gd32f4xx_usart.o(i.usart_enable)
    i.usart_flag_get                         0x08005f0a   Section        0  gd32f4xx_usart.o(i.usart_flag_get)
    i.usart_interrupt_disable                0x08005f28   Section        0  gd32f4xx_usart.o(i.usart_interrupt_disable)
    i.usart_interrupt_enable                 0x08005f42   Section        0  gd32f4xx_usart.o(i.usart_interrupt_enable)
    i.usart_interrupt_flag_clear             0x08005f5c   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    i.usart_interrupt_flag_get               0x08005f76   Section        0  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    i.usart_parity_config                    0x08005fae   Section        0  gd32f4xx_usart.o(i.usart_parity_config)
    i.usart_receive_config                   0x08005fbe   Section        0  gd32f4xx_usart.o(i.usart_receive_config)
    i.usart_stop_bit_set                     0x08005fce   Section        0  gd32f4xx_usart.o(i.usart_stop_bit_set)
    i.usart_transmit_config                  0x08005fde   Section        0  gd32f4xx_usart.o(i.usart_transmit_config)
    i.usart_word_length_set                  0x08005fee   Section        0  gd32f4xx_usart.o(i.usart_word_length_set)
    locale$$code                             0x08006000   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x0800602c   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$fpinit                             0x08006058   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08006058   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x08006062   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08006062   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08006066   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08006066   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x0800606a   Section       14  gd32f470vet6_bsp.o(.constdata)
    x$fpl$usenofp                            0x0800606a   Section        0  usenofp.o(x$fpl$usenofp)
    defaul_ebtn_param                        0x0800606a   Data          14  gd32f470vet6_bsp.o(.constdata)
    .constdata                               0x08006078   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08006078   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08006080   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08006080   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08006094   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x080060a8   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x080060a8   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x080060b9   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x080060b9   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x080060cc   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x080060e0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080060e0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800611c   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08006194   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08006198   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080061a0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080061ac   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080061ae   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080061af   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x080061b0   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x080061b0   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x080061b4   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080061bc   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x080062c0   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section      337  gd32f470vet6_bsp.o(.data)
    btns                                     0x20000048   Data         168  gd32f470vet6_bsp.o(.data)
    btns_combo                               0x200000f0   Data          72  gd32f470vet6_bsp.o(.data)
    temp_old                                 0x20000150   Data           1  gd32f470vet6_bsp.o(.data)
    .data                                    0x20000152   Section        2  main.o(.data)
    .data                                    0x20000154   Section        4  systick.o(.data)
    delay                                    0x20000154   Data           4  systick.o(.data)
    .data                                    0x20000158   Section       22  oled.o(.data)
    .data                                    0x20000170   Section       36  sdio_sdcard.o(.data)
    cardtype                                 0x20000178   Data           1  sdio_sdcard.o(.data)
    sd_rca                                   0x2000017a   Data           2  sdio_sdcard.o(.data)
    transmode                                0x2000017c   Data           4  sdio_sdcard.o(.data)
    totalnumber_bytes                        0x20000180   Data           4  sdio_sdcard.o(.data)
    stopcondition                            0x20000184   Data           4  sdio_sdcard.o(.data)
    transerror                               0x20000188   Data           1  sdio_sdcard.o(.data)
    transend                                 0x2000018c   Data           4  sdio_sdcard.o(.data)
    number_bytes                             0x20000190   Data           4  sdio_sdcard.o(.data)
    .data                                    0x20000194   Section        6  ff.o(.data)
    FatFs                                    0x20000194   Data           4  ff.o(.data)
    Fsid                                     0x20000198   Data           2  ff.o(.data)
    .data                                    0x2000019c   Section        4  system_gd32f4xx.o(.data)
    .bss                                     0x200001a0   Section     2716  gd32f470vet6_bsp.o(.bss)
    .bss                                     0x20000c3c   Section      256  main.o(.bss)
    .bss                                     0x20000d3c   Section       60  ebtn.o(.bss)
    ebtn_default                             0x20000d3c   Data          60  ebtn.o(.bss)
    .bss                                     0x20000d78   Section       32  sdio_sdcard.o(.bss)
    sd_csd                                   0x20000d78   Data          16  sdio_sdcard.o(.bss)
    sd_cid                                   0x20000d88   Data          16  sdio_sdcard.o(.bss)
    .bss                                     0x20000d98   Section       96  libspace.o(.bss)
    HEAP                                     0x20000df8   Section     1024  startup_gd32f450_470.o(HEAP)
    Heap_Mem                                 0x20000df8   Data        1024  startup_gd32f450_470.o(HEAP)
    STACK                                    0x200011f8   Section     1024  startup_gd32f450_470.o(STACK)
    Stack_Mem                                0x200011f8   Data        1024  startup_gd32f450_470.o(STACK)
    __initial_sp                             0x200015f8   Data           0  startup_gd32f450_470.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080001b5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080001b5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080001c3   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001e9   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001e9   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000245   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x08000261   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x08000261   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000267   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x0800026d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x08000273   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000279   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800027f   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000285   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800028f   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000295   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x0800029b   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080002a1   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080002a7   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080002ad   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080002b3   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080002b9   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x080002bf   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080002c5   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080002cb   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080002d5   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002db   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002e1   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002e7   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002ed   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002f1   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002f3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002fd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080002fd   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000309   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000309   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000309   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000313   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000315   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000317   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000319   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000319   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000319   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800031f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800031f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000323   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000323   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800032b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800032d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800032d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000331   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000339   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI10_15_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER3_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER6_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x08000353   Thumb Code     0  startup_gd32f450_470.o(.text)
    __user_initial_stackheap                 0x08000355   Thumb Code    10  startup_gd32f450_470.o(.text)
    vsnprintf                                0x08000379   Thumb Code    48  vsnprintf.o(.text)
    memcmp                                   0x080003ad   Thumb Code    88  memcmp.o(.text)
    __aeabi_memcpy                           0x08000405   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000405   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x0800046b   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x0800048f   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x0800048f   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x0800048f   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080004d7   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr                           0x080004f3   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x080004f3   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x080004f7   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x08000537   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000537   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000537   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800053b   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x08000585   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000605   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000607   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000609   Thumb Code     2  heapauxi.o(.text)
    _printf_pre_padding                      0x0800060b   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000637   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000659   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x0800066b   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x0800067d   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080006d1   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000749   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x0800077b   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x080007a1   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x080007ab   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x080007bd   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000879   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x080008f5   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000937   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x0800094f   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000965   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x080009bb   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x080009d7   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x080009e3   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x080009f9   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    _ll_udiv10                               0x08000b81   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000c0b   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000cbd   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000e6f   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x080010dd   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x080013d9   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080013ed   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080013fd   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001405   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001419   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001429   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x08001431   Thumb Code    64  _wcrtomb.o(.text)
    __user_setup_stackheap                   0x08001471   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x080014bd   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x080014cd   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x080014d5   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001555   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001639   Thumb Code    18  exit.o(.text)
    __user_libspace                          0x0800164d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x0800164d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x0800164d   Thumb Code     0  libspace.o(.text)
    _sys_exit                                0x08001655   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08001661   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001661   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001663   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x08001663   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080016a1   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x080016e7   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001747   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001a7f   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001b5b   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001b85   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001baf   Thumb Code   580  btod.o(CL$$btod_mult_common)
    AdcPeriphInit                            0x08001e61   Thumb Code   158  gd32f470vet6_bsp.o(i.AdcPeriphInit)
    AdcTask                                  0x08001f09   Thumb Code    62  adc_app.o(i.AdcTask)
    BcdToDec                                 0x08001f5d   Thumb Code    20  gd32f470vet6_bsp.o(i.BcdToDec)
    BtnEventCallback                         0x08001f71   Thumb Code   204  btn_app.o(i.BtnEventCallback)
    BtnPeriphInit                            0x080020e5   Thumb Code    96  gd32f470vet6_bsp.o(i.BtnPeriphInit)
    BtnTask                                  0x0800215d   Thumb Code    12  btn_app.o(i.BtnTask)
    BusFault_Handler                         0x0800216d   Thumb Code     4  gd32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x08002171   Thumb Code     4  gd32f4xx_it.o(i.DebugMon_Handler)
    DecToBcd                                 0x08002175   Thumb Code    26  gd32f470vet6_bsp.o(i.DecToBcd)
    FlashPeriphInit                          0x08002191   Thumb Code   144  gd32f470vet6_bsp.o(i.FlashPeriphInit)
    HardFault_Handler                        0x08002229   Thumb Code     4  gd32f4xx_it.o(i.HardFault_Handler)
    LedDisp                                  0x08002355   Thumb Code   126  gd32f470vet6_bsp.o(i.LedDisp)
    LedPeriphInit                            0x080023dd   Thumb Code    50  gd32f470vet6_bsp.o(i.LedPeriphInit)
    LedTask                                  0x08002415   Thumb Code    16  led_app.o(i.LedTask)
    MemManage_Handler                        0x08002429   Thumb Code     4  gd32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800242d   Thumb Code     4  gd32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08002459   Thumb Code    56  oled.o(i.OLED_Clear)
    OLED_Init                                0x08002491   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x080024c1   Thumb Code    36  oled.o(i.OLED_Set_Position)
    OLED_Write_cmd                           0x080024e5   Thumb Code   282  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x0800260d   Thumb Code   282  oled.o(i.OLED_Write_data)
    OledPeriphInit                           0x0800279d   Thumb Code   146  gd32f470vet6_bsp.o(i.OledPeriphInit)
    PendSV_Handler                           0x0800283d   Thumb Code     4  gd32f4xx_it.o(i.PendSV_Handler)
    ReadRtc                                  0x08002841   Thumb Code   106  gd32f470vet6_bsp.o(i.ReadRtc)
    RtcPeriphInit                            0x080028ad   Thumb Code   138  gd32f470vet6_bsp.o(i.RtcPeriphInit)
    RtcTask                                  0x08002a11   Thumb Code    52  rtc_app.o(i.RtcTask)
    SDIO_IRQHandler                          0x08002a81   Thumb Code     8  gd32f4xx_it.o(i.SDIO_IRQHandler)
    SVC_Handler                              0x08002a89   Thumb Code     4  gd32f4xx_it.o(i.SVC_Handler)
    SetRtc                                   0x08002a8d   Thumb Code   122  gd32f470vet6_bsp.o(i.SetRtc)
    SysInit                                  0x08002b31   Thumb Code    40  gd32f470vet6_bsp.o(i.SysInit)
    SysTick_Handler                          0x08002b59   Thumb Code    18  gd32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x08002b71   Thumb Code   364  system_gd32f4xx.o(i.SystemInit)
    TaskExeution                             0x08002ced   Thumb Code    78  scheduler.o(i.TaskExeution)
    TfPeriphInit                             0x08002d49   Thumb Code    72  gd32f470vet6_bsp.o(i.TfPeriphInit)
    USART0_IRQHandler                        0x08002dd1   Thumb Code   138  gd32f4xx_it.o(i.USART0_IRQHandler)
    USART1_IRQHandler                        0x08002e75   Thumb Code    60  main.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08002ec1   Thumb Code     4  gd32f4xx_it.o(i.UsageFault_Handler)
    Usart0PeriphInit                         0x08002f31   Thumb Code   170  gd32f470vet6_bsp.o(i.Usart0PeriphInit)
    Usart0Printf                             0x08002fe5   Thumb Code   102  gd32f470vet6_bsp.o(i.Usart0Printf)
    Usart0Task                               0x08003055   Thumb Code    38  usart_app.o(i.Usart0Task)
    __ARM_fpclassify                         0x0800308d   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x080030bd   Thumb Code    14  __printf_wp.o(i._is_digit)
    adc_calibration_enable                   0x080030e7   Thumb Code    42  gd32f4xx_adc.o(i.adc_calibration_enable)
    adc_channel_length_config                0x08003111   Thumb Code    82  gd32f4xx_adc.o(i.adc_channel_length_config)
    adc_clock_config                         0x08003165   Thumb Code    28  gd32f4xx_adc.o(i.adc_clock_config)
    adc_data_alignment_config                0x08003189   Thumb Code    22  gd32f4xx_adc.o(i.adc_data_alignment_config)
    adc_dma_mode_enable                      0x0800319f   Thumb Code    10  gd32f4xx_adc.o(i.adc_dma_mode_enable)
    adc_dma_request_after_last_enable        0x080031a9   Thumb Code    10  gd32f4xx_adc.o(i.adc_dma_request_after_last_enable)
    adc_enable                               0x080031b3   Thumb Code    18  gd32f4xx_adc.o(i.adc_enable)
    adc_external_trigger_config              0x080031c5   Thumb Code    52  gd32f4xx_adc.o(i.adc_external_trigger_config)
    adc_external_trigger_source_config       0x080031f9   Thumb Code    48  gd32f4xx_adc.o(i.adc_external_trigger_source_config)
    adc_routine_channel_config               0x08003229   Thumb Code   172  gd32f4xx_adc.o(i.adc_routine_channel_config)
    adc_software_trigger_enable              0x080032d5   Thumb Code    36  gd32f4xx_adc.o(i.adc_software_trigger_enable)
    adc_special_function_config              0x080032f9   Thumb Code    90  gd32f4xx_adc.o(i.adc_special_function_config)
    adc_sync_mode_config                     0x08003355   Thumb Code    28  gd32f4xx_adc.o(i.adc_sync_mode_config)
    delay_1ms                                0x080034b1   Thumb Code    16  systick.o(i.delay_1ms)
    delay_decrement                          0x080034c5   Thumb Code    18  systick.o(i.delay_decrement)
    disk_initialize                          0x080034dd   Thumb Code   134  diskio.o(i.disk_initialize)
    dma_channel_disable                      0x08003563   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_disable)
    dma_channel_enable                       0x08003583   Thumb Code    32  gd32f4xx_dma.o(i.dma_channel_enable)
    dma_channel_subperipheral_select         0x080035a3   Thumb Code    38  gd32f4xx_dma.o(i.dma_channel_subperipheral_select)
    dma_circulation_disable                  0x080035c9   Thumb Code    32  gd32f4xx_dma.o(i.dma_circulation_disable)
    dma_circulation_enable                   0x080035e9   Thumb Code    32  gd32f4xx_dma.o(i.dma_circulation_enable)
    dma_deinit                               0x08003609   Thumb Code   166  gd32f4xx_dma.o(i.dma_deinit)
    dma_flag_clear                           0x080036af   Thumb Code    62  gd32f4xx_dma.o(i.dma_flag_clear)
    dma_flag_get                             0x080036ed   Thumb Code    76  gd32f4xx_dma.o(i.dma_flag_get)
    dma_memory_address_config                0x08003739   Thumb Code    32  gd32f4xx_dma.o(i.dma_memory_address_config)
    dma_single_data_mode_init                0x08003759   Thumb Code   340  gd32f4xx_dma.o(i.dma_single_data_mode_init)
    dma_single_data_para_struct_init         0x080038b1   Thumb Code    34  gd32f4xx_dma.o(i.dma_single_data_para_struct_init)
    dma_transfer_number_config               0x080038d3   Thumb Code    16  gd32f4xx_dma.o(i.dma_transfer_number_config)
    dma_transfer_number_get                  0x080038e3   Thumb Code    16  gd32f4xx_dma.o(i.dma_transfer_number_get)
    ebtn_combo_btn_add_btn                   0x080038f3   Thumb Code    32  ebtn.o(i.ebtn_combo_btn_add_btn)
    ebtn_combo_btn_add_btn_by_idx            0x08003913   Thumb Code    32  ebtn.o(i.ebtn_combo_btn_add_btn_by_idx)
    ebtn_get_btn_index_by_key_id             0x08003935   Thumb Code    66  ebtn.o(i.ebtn_get_btn_index_by_key_id)
    ebtn_init                                0x080039d5   Thumb Code    88  ebtn.o(i.ebtn_init)
    ebtn_process                             0x08003a31   Thumb Code    26  ebtn.o(i.ebtn_process)
    ebtn_process_with_curr_state             0x08003b05   Thumb Code   444  ebtn.o(i.ebtn_process_with_curr_state)
    ebtn_set_combo_suppress_threshold        0x08003cc5   Thumb Code     6  ebtn.o(i.ebtn_set_combo_suppress_threshold)
    ebtn_set_config                          0x08003cd1   Thumb Code     8  ebtn.o(i.ebtn_set_config)
    f_mount                                  0x08003ce5   Thumb Code    38  ff.o(i.f_mount)
    gpio_af_set                              0x08003d11   Thumb Code    94  gd32f4xx_gpio.o(i.gpio_af_set)
    gpio_bit_reset                           0x08003d6f   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_reset)
    gpio_bit_set                             0x08003d73   Thumb Code     4  gd32f4xx_gpio.o(i.gpio_bit_set)
    gpio_bit_write                           0x08003d77   Thumb Code    10  gd32f4xx_gpio.o(i.gpio_bit_write)
    gpio_input_bit_get                       0x08003df5   Thumb Code    16  gd32f4xx_gpio.o(i.gpio_input_bit_get)
    gpio_mode_set                            0x08003e05   Thumb Code    78  gd32f4xx_gpio.o(i.gpio_mode_set)
    gpio_output_options_set                  0x08003e53   Thumb Code    66  gd32f4xx_gpio.o(i.gpio_output_options_set)
    i2c_ack_config                           0x08003e95   Thumb Code    16  gd32f4xx_i2c.o(i.i2c_ack_config)
    i2c_clock_config                         0x08003ea5   Thumb Code   216  gd32f4xx_i2c.o(i.i2c_clock_config)
    i2c_deinit                               0x08003f89   Thumb Code    84  gd32f4xx_i2c.o(i.i2c_deinit)
    i2c_dma_config                           0x08003fe1   Thumb Code    16  gd32f4xx_i2c.o(i.i2c_dma_config)
    i2c_enable                               0x08003ff1   Thumb Code    10  gd32f4xx_i2c.o(i.i2c_enable)
    i2c_flag_clear                           0x08003ffb   Thumb Code    40  gd32f4xx_i2c.o(i.i2c_flag_clear)
    i2c_flag_get                             0x08004023   Thumb Code    30  gd32f4xx_i2c.o(i.i2c_flag_get)
    i2c_master_addressing                    0x08004041   Thumb Code    20  gd32f4xx_i2c.o(i.i2c_master_addressing)
    i2c_mode_addr_config                     0x08004055   Thumb Code    28  gd32f4xx_i2c.o(i.i2c_mode_addr_config)
    i2c_start_on_bus                         0x08004071   Thumb Code    10  gd32f4xx_i2c.o(i.i2c_start_on_bus)
    i2c_stop_on_bus                          0x0800407b   Thumb Code    10  gd32f4xx_i2c.o(i.i2c_stop_on_bus)
    main                                     0x08004085   Thumb Code    68  main.o(i.main)
    nvic_irq_enable                          0x08004101   Thumb Code   186  gd32f4xx_misc.o(i.nvic_irq_enable)
    nvic_priority_group_set                  0x080041c5   Thumb Code    10  gd32f4xx_misc.o(i.nvic_priority_group_set)
    pmu_backup_write_enable                  0x080041d9   Thumb Code    14  gd32f4xx_pmu.o(i.pmu_backup_write_enable)
    rcu_all_reset_flag_clear                 0x08004865   Thumb Code    14  gd32f4xx_rcu.o(i.rcu_all_reset_flag_clear)
    rcu_clock_freq_get                       0x08004879   Thumb Code   264  gd32f4xx_rcu.o(i.rcu_clock_freq_get)
    rcu_flag_get                             0x080049c1   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_flag_get)
    rcu_osci_on                              0x080049e5   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_osci_on)
    rcu_osci_stab_wait                       0x08004a09   Thumb Code   342  gd32f4xx_rcu.o(i.rcu_osci_stab_wait)
    rcu_periph_clock_enable                  0x08004b65   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_clock_enable)
    rcu_periph_reset_disable                 0x08004b89   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_disable)
    rcu_periph_reset_enable                  0x08004bad   Thumb Code    32  gd32f4xx_rcu.o(i.rcu_periph_reset_enable)
    rcu_rtc_clock_config                     0x08004bd1   Thumb Code    18  gd32f4xx_rcu.o(i.rcu_rtc_clock_config)
    rs485_receive_enable                     0x08004be9   Thumb Code    10  main.o(i.rs485_receive_enable)
    rs485_send_enable                        0x08004bf5   Thumb Code    18  main.o(i.rs485_send_enable)
    rtc_current_time_get                     0x08004c0d   Thumb Code    96  gd32f4xx_rtc.o(i.rtc_current_time_get)
    rtc_init                                 0x08004c71   Thumb Code   190  gd32f4xx_rtc.o(i.rtc_init)
    rtc_init_mode_enter                      0x08004d35   Thumb Code    66  gd32f4xx_rtc.o(i.rtc_init_mode_enter)
    rtc_init_mode_exit                       0x08004d7d   Thumb Code    14  gd32f4xx_rtc.o(i.rtc_init_mode_exit)
    rtc_register_sync_wait                   0x08004d91   Thumb Code    92  gd32f4xx_rtc.o(i.rtc_register_sync_wait)
    sd_bus_mode_config                       0x08004df1   Thumb Code   144  sdio_sdcard.o(i.sd_bus_mode_config)
    sd_card_information_get                  0x08004f81   Thumb Code   686  sdio_sdcard.o(i.sd_card_information_get)
    sd_card_init                             0x08005241   Thumb Code   268  sdio_sdcard.o(i.sd_card_init)
    sd_card_select_deselect                  0x0800535d   Thumb Code    38  sdio_sdcard.o(i.sd_card_select_deselect)
    sd_cardstatus_get                        0x08005385   Thumb Code    66  sdio_sdcard.o(i.sd_cardstatus_get)
    sd_init                                  0x080053cd   Thumb Code    70  sdio_sdcard.o(i.sd_init)
    sd_interrupts_process                    0x08005415   Thumb Code   286  sdio_sdcard.o(i.sd_interrupts_process)
    sd_power_on                              0x08005545   Thumb Code   290  sdio_sdcard.o(i.sd_power_on)
    sd_transfer_mode_config                  0x080057cd   Thumb Code    20  sdio_sdcard.o(i.sd_transfer_mode_config)
    sd_transfer_stop                         0x080057e5   Thumb Code    36  sdio_sdcard.o(i.sd_transfer_stop)
    sdio_bus_mode_set                        0x08005809   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_bus_mode_set)
    sdio_clock_config                        0x08005825   Thumb Code    44  gd32f4xx_sdio.o(i.sdio_clock_config)
    sdio_clock_enable                        0x08005859   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_clock_enable)
    sdio_command_index_get                   0x0800586d   Thumb Code     8  gd32f4xx_sdio.o(i.sdio_command_index_get)
    sdio_command_response_config             0x08005879   Thumb Code    52  gd32f4xx_sdio.o(i.sdio_command_response_config)
    sdio_csm_enable                          0x080058b1   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_csm_enable)
    sdio_data_config                         0x080058c5   Thumb Code    54  gd32f4xx_sdio.o(i.sdio_data_config)
    sdio_data_read                           0x08005901   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_data_read)
    sdio_data_transfer_config                0x0800590d   Thumb Code    24  gd32f4xx_sdio.o(i.sdio_data_transfer_config)
    sdio_deinit                              0x08005929   Thumb Code    20  gd32f4xx_sdio.o(i.sdio_deinit)
    sdio_dsm_enable                          0x0800593d   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_dsm_enable)
    sdio_flag_clear                          0x08005951   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_flag_clear)
    sdio_flag_get                            0x0800595d   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_flag_get)
    sdio_hardware_clock_disable              0x08005971   Thumb Code    14  gd32f4xx_sdio.o(i.sdio_hardware_clock_disable)
    sdio_interrupt_disable                   0x08005985   Thumb Code    12  gd32f4xx_sdio.o(i.sdio_interrupt_disable)
    sdio_interrupt_flag_clear                0x08005995   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_interrupt_flag_clear)
    sdio_interrupt_flag_get                  0x080059a1   Thumb Code    16  gd32f4xx_sdio.o(i.sdio_interrupt_flag_get)
    sdio_power_state_get                     0x080059b5   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_get)
    sdio_power_state_set                     0x080059c1   Thumb Code     6  gd32f4xx_sdio.o(i.sdio_power_state_set)
    sdio_response_get                        0x080059cd   Thumb Code    56  gd32f4xx_sdio.o(i.sdio_response_get)
    sdio_wait_type_set                       0x08005a09   Thumb Code    22  gd32f4xx_sdio.o(i.sdio_wait_type_set)
    spi_enable                               0x08005a25   Thumb Code    10  gd32f4xx_spi.o(i.spi_enable)
    spi_flash_init                           0x08005a31   Thumb Code    20  gd25qxx.o(i.spi_flash_init)
    spi_init                                 0x08005a4d   Thumb Code    50  gd32f4xx_spi.o(i.spi_init)
    systick_config                           0x08005b99   Thumb Code    74  systick.o(i.systick_config)
    usart1_enable_receive_interrupt          0x08005be9   Thumb Code    24  main.o(i.usart1_enable_receive_interrupt)
    usart1_rs485_init                        0x08005c05   Thumb Code   178  main.o(i.usart1_rs485_init)
    usart1_send_byte                         0x08005cc1   Thumb Code    28  main.o(i.usart1_send_byte)
    usart1_send_string                       0x08005ce1   Thumb Code    48  main.o(i.usart1_send_string)
    usart_baudrate_set                       0x08005d15   Thumb Code   224  gd32f4xx_usart.o(i.usart_baudrate_set)
    usart_data_receive                       0x08005dfd   Thumb Code    10  gd32f4xx_usart.o(i.usart_data_receive)
    usart_data_transmit                      0x08005e07   Thumb Code     8  gd32f4xx_usart.o(i.usart_data_transmit)
    usart_deinit                             0x08005e11   Thumb Code   210  gd32f4xx_usart.o(i.usart_deinit)
    usart_dma_receive_config                 0x08005eed   Thumb Code    20  gd32f4xx_usart.o(i.usart_dma_receive_config)
    usart_enable                             0x08005f01   Thumb Code    10  gd32f4xx_usart.o(i.usart_enable)
    usart_flag_get                           0x08005f0b   Thumb Code    30  gd32f4xx_usart.o(i.usart_flag_get)
    usart_interrupt_disable                  0x08005f29   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_disable)
    usart_interrupt_enable                   0x08005f43   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_enable)
    usart_interrupt_flag_clear               0x08005f5d   Thumb Code    26  gd32f4xx_usart.o(i.usart_interrupt_flag_clear)
    usart_interrupt_flag_get                 0x08005f77   Thumb Code    56  gd32f4xx_usart.o(i.usart_interrupt_flag_get)
    usart_parity_config                      0x08005faf   Thumb Code    16  gd32f4xx_usart.o(i.usart_parity_config)
    usart_receive_config                     0x08005fbf   Thumb Code    16  gd32f4xx_usart.o(i.usart_receive_config)
    usart_stop_bit_set                       0x08005fcf   Thumb Code    16  gd32f4xx_usart.o(i.usart_stop_bit_set)
    usart_transmit_config                    0x08005fdf   Thumb Code    16  gd32f4xx_usart.o(i.usart_transmit_config)
    usart_word_length_set                    0x08005fef   Thumb Code    16  gd32f4xx_usart.o(i.usart_word_length_set)
    _get_lc_numeric                          0x08006001   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x0800602d   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _fp_init                                 0x08006059   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08006061   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08006061   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x08006063   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08006067   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x0800606a   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08006174   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006194   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080061bd   Data           0  lc_ctype_c.o(locale$$data)
    uwTick                                   0x20000000   Data           4  gd32f470vet6_bsp.o(.data)
    schedul_task                             0x20000004   Data          60  gd32f470vet6_bsp.o(.data)
    task_num                                 0x20000040   Data           1  gd32f470vet6_bsp.o(.data)
    ucLed                                    0x20000041   Data           6  gd32f470vet6_bsp.o(.data)
    adc0_voltage                             0x20000138   Data           4  gd32f470vet6_bsp.o(.data)
    usart0_rx_flag                           0x2000013c   Data           1  gd32f470vet6_bsp.o(.data)
    oled_cmd_buffer                          0x2000013d   Data           2  gd32f470vet6_bsp.o(.data)
    oled_data_buffer                         0x2000013f   Data           2  gd32f470vet6_bsp.o(.data)
    prescaler_a                              0x20000144   Data           4  gd32f470vet6_bsp.o(.data)
    prescaler_s                              0x20000148   Data           4  gd32f470vet6_bsp.o(.data)
    rtcsrc_flag                              0x2000014c   Data           4  gd32f470vet6_bsp.o(.data)
    rx_count                                 0x20000152   Data           2  main.o(.data)
    initcmd1                                 0x20000158   Data          22  oled.o(.data)
    sd_scr                                   0x20000170   Data           8  sdio_sdcard.o(.data)
    SystemCoreClock                          0x2000019c   Data           4  system_gd32f4xx.o(.data)
    adc0_value                               0x200001a0   Data          64  gd32f470vet6_bsp.o(.bss)
    usart0_rx_buffer_dma                     0x200001e0   Data        1024  gd32f470vet6_bsp.o(.bss)
    usart0_rx_buffer_proc                    0x200005e0   Data        1024  gd32f470vet6_bsp.o(.bss)
    spi1_tx_buffer                           0x200009e0   Data          12  gd32f470vet6_bsp.o(.bss)
    spi1_rx_buffer                           0x200009ec   Data          12  gd32f470vet6_bsp.o(.bss)
    fs                                       0x200009f8   Data         560  gd32f470vet6_bsp.o(.bss)
    ucRtc                                    0x20000c28   Data          20  gd32f470vet6_bsp.o(.bss)
    rx_buffer                                0x20000c3c   Data         256  main.o(.bss)
    __libspace_start                         0x20000d98   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000df8   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006460, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x00006330])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000062c0, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         1760    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000008   Code   RO         7267  * !!!main             c_w.l(__main.o)
    0x080001b4   0x080001b4   0x00000034   Code   RO         7598    !!!scatter          c_w.l(__scatter.o)
    0x080001e8   0x080001e8   0x0000005a   Code   RO         7596    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x08000242   0x08000242   0x00000002   PAD
    0x08000244   0x08000244   0x0000001c   Code   RO         7600    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000260   0x08000260   0x00000000   Code   RO         7355    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000260   0x08000260   0x00000006   Code   RO         7344    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000266   0x08000266   0x00000006   Code   RO         7346    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x0800026c   0x0800026c   0x00000006   Code   RO         7351    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000272   0x08000272   0x00000006   Code   RO         7352    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000278   0x08000278   0x00000006   Code   RO         7353    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800027e   0x0800027e   0x00000006   Code   RO         7354    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000284   0x08000284   0x0000000a   Code   RO         7359    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800028e   0x0800028e   0x00000006   Code   RO         7348    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000294   0x08000294   0x00000006   Code   RO         7349    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x0800029a   0x0800029a   0x00000006   Code   RO         7350    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080002a0   0x080002a0   0x00000006   Code   RO         7347    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080002a6   0x080002a6   0x00000006   Code   RO         7345    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080002ac   0x080002ac   0x00000006   Code   RO         7356    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080002b2   0x080002b2   0x00000006   Code   RO         7357    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080002b8   0x080002b8   0x00000006   Code   RO         7358    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x080002be   0x080002be   0x00000006   Code   RO         7363    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080002c4   0x080002c4   0x00000006   Code   RO         7364    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080002ca   0x080002ca   0x0000000a   Code   RO         7360    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080002d4   0x080002d4   0x00000006   Code   RO         7342    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002da   0x080002da   0x00000006   Code   RO         7343    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002e0   0x080002e0   0x00000006   Code   RO         7361    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002e6   0x080002e6   0x00000006   Code   RO         7362    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002ec   0x080002ec   0x00000004   Code   RO         7408    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002f0   0x080002f0   0x00000002   Code   RO         7457    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002f2   0x080002f2   0x00000004   Code   RO         7470    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         7473    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         7476    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         7478    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         7480    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000006   Code   RO         7481    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002fc   0x080002fc   0x00000000   Code   RO         7483    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002fc   0x080002fc   0x0000000c   Code   RO         7484    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000308   0x08000308   0x00000000   Code   RO         7485    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000308   0x08000308   0x00000000   Code   RO         7487    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000308   0x08000308   0x0000000a   Code   RO         7488    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7489    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7491    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7493    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7495    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7497    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7499    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7501    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7503    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7507    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7509    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7511    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000000   Code   RO         7513    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000312   0x08000312   0x00000002   Code   RO         7514    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000314   0x08000314   0x00000002   Code   RO         7534    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7547    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7549    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7551    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7554    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7557    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7559    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000000   Code   RO         7562    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000316   0x08000316   0x00000002   Code   RO         7563    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000318   0x08000318   0x00000000   Code   RO         7271    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000318   0x08000318   0x00000000   Code   RO         7379    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000318   0x08000318   0x00000006   Code   RO         7391    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800031e   0x0800031e   0x00000000   Code   RO         7381    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800031e   0x0800031e   0x00000004   Code   RO         7382    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000322   0x08000322   0x00000000   Code   RO         7384    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000322   0x08000322   0x00000008   Code   RO         7385    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800032a   0x0800032a   0x00000002   Code   RO         7465    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800032c   0x0800032c   0x00000000   Code   RO         7516    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800032c   0x0800032c   0x00000004   Code   RO         7517    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000330   0x08000330   0x00000006   Code   RO         7518    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000336   0x08000336   0x00000002   PAD
    0x08000338   0x08000338   0x00000040   Code   RO         1761    .text               startup_gd32f450_470.o
    0x08000378   0x08000378   0x00000034   Code   RO         7247    .text               c_w.l(vsnprintf.o)
    0x080003ac   0x080003ac   0x00000058   Code   RO         7253    .text               c_w.l(memcmp.o)
    0x08000404   0x08000404   0x0000008a   Code   RO         7255    .text               c_w.l(rt_memcpy_v6.o)
    0x0800048e   0x0800048e   0x00000064   Code   RO         7257    .text               c_w.l(rt_memcpy_w.o)
    0x080004f2   0x080004f2   0x00000044   Code   RO         7259    .text               c_w.l(rt_memclr.o)
    0x08000536   0x08000536   0x0000004e   Code   RO         7261    .text               c_w.l(rt_memclr_w.o)
    0x08000584   0x08000584   0x00000080   Code   RO         7263    .text               c_w.l(strcmpv7m.o)
    0x08000604   0x08000604   0x00000006   Code   RO         7265    .text               c_w.l(heapauxi.o)
    0x0800060a   0x0800060a   0x0000004e   Code   RO         7274    .text               c_w.l(_printf_pad.o)
    0x08000658   0x08000658   0x00000024   Code   RO         7276    .text               c_w.l(_printf_truncate.o)
    0x0800067c   0x0800067c   0x00000052   Code   RO         7278    .text               c_w.l(_printf_str.o)
    0x080006ce   0x080006ce   0x00000002   PAD
    0x080006d0   0x080006d0   0x00000078   Code   RO         7280    .text               c_w.l(_printf_dec.o)
    0x08000748   0x08000748   0x00000028   Code   RO         7282    .text               c_w.l(_printf_charcount.o)
    0x08000770   0x08000770   0x00000030   Code   RO         7284    .text               c_w.l(_printf_char_common.o)
    0x080007a0   0x080007a0   0x0000000a   Code   RO         7286    .text               c_w.l(_sputc.o)
    0x080007aa   0x080007aa   0x00000010   Code   RO         7288    .text               c_w.l(_snputc.o)
    0x080007ba   0x080007ba   0x00000002   PAD
    0x080007bc   0x080007bc   0x000000bc   Code   RO         7290    .text               c_w.l(_printf_wctomb.o)
    0x08000878   0x08000878   0x0000007c   Code   RO         7293    .text               c_w.l(_printf_longlong_dec.o)
    0x080008f4   0x080008f4   0x00000070   Code   RO         7299    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000964   0x08000964   0x00000094   Code   RO         7319    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x080009f8   0x080009f8   0x00000188   Code   RO         7339    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000b80   0x08000b80   0x0000008a   Code   RO         7393    .text               c_w.l(lludiv10.o)
    0x08000c0a   0x08000c0a   0x000000b2   Code   RO         7395    .text               c_w.l(_printf_intcommon.o)
    0x08000cbc   0x08000cbc   0x0000041e   Code   RO         7397    .text               c_w.l(_printf_fp_dec.o)
    0x080010da   0x080010da   0x00000002   PAD
    0x080010dc   0x080010dc   0x000002fc   Code   RO         7399    .text               c_w.l(_printf_fp_hex.o)
    0x080013d8   0x080013d8   0x0000002c   Code   RO         7404    .text               c_w.l(_printf_char.o)
    0x08001404   0x08001404   0x0000002c   Code   RO         7406    .text               c_w.l(_printf_wchar.o)
    0x08001430   0x08001430   0x00000040   Code   RO         7409    .text               c_w.l(_wcrtomb.o)
    0x08001470   0x08001470   0x0000004a   Code   RO         7411    .text               c_w.l(sys_stackheap_outer.o)
    0x080014ba   0x080014ba   0x00000002   PAD
    0x080014bc   0x080014bc   0x00000010   Code   RO         7413    .text               c_w.l(rt_ctype_table.o)
    0x080014cc   0x080014cc   0x00000008   Code   RO         7418    .text               c_w.l(rt_locale_intlibspace.o)
    0x080014d4   0x080014d4   0x00000080   Code   RO         7420    .text               c_w.l(_printf_fp_infnan.o)
    0x08001554   0x08001554   0x000000e4   Code   RO         7422    .text               c_w.l(bigflt0.o)
    0x08001638   0x08001638   0x00000012   Code   RO         7450    .text               c_w.l(exit.o)
    0x0800164a   0x0800164a   0x00000002   PAD
    0x0800164c   0x0800164c   0x00000008   Code   RO         7462    .text               c_w.l(libspace.o)
    0x08001654   0x08001654   0x0000000c   Code   RO         7526    .text               c_w.l(sys_exit.o)
    0x08001660   0x08001660   0x00000002   Code   RO         7537    .text               c_w.l(use_no_semi.o)
    0x08001662   0x08001662   0x00000000   Code   RO         7539    .text               c_w.l(indicate_semi.o)
    0x08001662   0x08001662   0x0000003e   Code   RO         7425    CL$$btod_d2e        c_w.l(btod.o)
    0x080016a0   0x080016a0   0x00000046   Code   RO         7427    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x080016e6   0x080016e6   0x00000060   Code   RO         7426    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001746   0x08001746   0x00000338   Code   RO         7435    CL$$btod_div_common  c_w.l(btod.o)
    0x08001a7e   0x08001a7e   0x000000dc   Code   RO         7432    CL$$btod_e2e        c_w.l(btod.o)
    0x08001b5a   0x08001b5a   0x0000002a   Code   RO         7429    CL$$btod_ediv       c_w.l(btod.o)
    0x08001b84   0x08001b84   0x0000002a   Code   RO         7428    CL$$btod_emul       c_w.l(btod.o)
    0x08001bae   0x08001bae   0x00000244   Code   RO         7434    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001df2   0x08001df2   0x00000002   PAD
    0x08001df4   0x08001df4   0x0000006c   Code   RO          278    i.AdcDmaInit        gd32f470vet6_bsp.o
    0x08001e60   0x08001e60   0x000000a8   Code   RO          279    i.AdcPeriphInit     gd32f470vet6_bsp.o
    0x08001f08   0x08001f08   0x00000054   Code   RO          553    i.AdcTask           adc_app.o
    0x08001f5c   0x08001f5c   0x00000014   Code   RO          280    i.BcdToDec          gd32f470vet6_bsp.o
    0x08001f70   0x08001f70   0x000000d0   Code   RO          593    i.BtnEventCallback  btn_app.o
    0x08002040   0x08002040   0x000000a4   Code   RO          281    i.BtnGetState       gd32f470vet6_bsp.o
    0x080020e4   0x080020e4   0x00000078   Code   RO          282    i.BtnPeriphInit     gd32f470vet6_bsp.o
    0x0800215c   0x0800215c   0x00000010   Code   RO          594    i.BtnTask           btn_app.o
    0x0800216c   0x0800216c   0x00000004   Code   RO            3    i.BusFault_Handler  gd32f4xx_it.o
    0x08002170   0x08002170   0x00000004   Code   RO            4    i.DebugMon_Handler  gd32f4xx_it.o
    0x08002174   0x08002174   0x0000001a   Code   RO          283    i.DecToBcd          gd32f470vet6_bsp.o
    0x0800218e   0x0800218e   0x00000002   PAD
    0x08002190   0x08002190   0x00000098   Code   RO          284    i.FlashPeriphInit   gd32f470vet6_bsp.o
    0x08002228   0x08002228   0x00000004   Code   RO            5    i.HardFault_Handler  gd32f4xx_it.o
    0x0800222c   0x0800222c   0x00000128   Code   RO          912    i.I2C_Bus_Reset     oled.o
    0x08002354   0x08002354   0x00000088   Code   RO          285    i.LedDisp           gd32f470vet6_bsp.o
    0x080023dc   0x080023dc   0x00000038   Code   RO          286    i.LedPeriphInit     gd32f470vet6_bsp.o
    0x08002414   0x08002414   0x00000014   Code   RO          573    i.LedTask           led_app.o
    0x08002428   0x08002428   0x00000004   Code   RO            6    i.MemManage_Handler  gd32f4xx_it.o
    0x0800242c   0x0800242c   0x00000004   Code   RO            7    i.NMI_Handler       gd32f4xx_it.o
    0x08002430   0x08002430   0x00000028   Code   RO          507    i.NVIC_SetPriority  systick.o
    0x08002458   0x08002458   0x00000038   Code   RO          914    i.OLED_Clear        oled.o
    0x08002490   0x08002490   0x00000030   Code   RO          917    i.OLED_Init         oled.o
    0x080024c0   0x080024c0   0x00000024   Code   RO          919    i.OLED_Set_Position  oled.o
    0x080024e4   0x080024e4   0x00000128   Code   RO          927    i.OLED_Write_cmd    oled.o
    0x0800260c   0x0800260c   0x00000128   Code   RO          928    i.OLED_Write_data   oled.o
    0x08002734   0x08002734   0x00000068   Code   RO          287    i.OledDmaInit       gd32f470vet6_bsp.o
    0x0800279c   0x0800279c   0x000000a0   Code   RO          289    i.OledPeriphInit    gd32f470vet6_bsp.o
    0x0800283c   0x0800283c   0x00000004   Code   RO            8    i.PendSV_Handler    gd32f4xx_it.o
    0x08002840   0x08002840   0x0000006a   Code   RO          290    i.ReadRtc           gd32f470vet6_bsp.o
    0x080028aa   0x080028aa   0x00000002   PAD
    0x080028ac   0x080028ac   0x00000124   Code   RO          291    i.RtcPeriphInit     gd32f470vet6_bsp.o
    0x080029d0   0x080029d0   0x00000040   Code   RO          292    i.RtcPreConfig      gd32f470vet6_bsp.o
    0x08002a10   0x08002a10   0x00000070   Code   RO          639    i.RtcTask           rtc_app.o
    0x08002a80   0x08002a80   0x00000008   Code   RO            9    i.SDIO_IRQHandler   gd32f4xx_it.o
    0x08002a88   0x08002a88   0x00000004   Code   RO           10    i.SVC_Handler       gd32f4xx_it.o
    0x08002a8c   0x08002a8c   0x000000a4   Code   RO          293    i.SetRtc            gd32f470vet6_bsp.o
    0x08002b30   0x08002b30   0x00000028   Code   RO          294    i.SysInit           gd32f470vet6_bsp.o
    0x08002b58   0x08002b58   0x00000018   Code   RO           11    i.SysTick_Handler   gd32f4xx_it.o
    0x08002b70   0x08002b70   0x0000017c   Code   RO         1708    i.SystemInit        system_gd32f4xx.o
    0x08002cec   0x08002cec   0x0000005c   Code   RO          487    i.TaskExeution      scheduler.o
    0x08002d48   0x08002d48   0x00000088   Code   RO          295    i.TfPeriphInit      gd32f470vet6_bsp.o
    0x08002dd0   0x08002dd0   0x000000a4   Code   RO           12    i.USART0_IRQHandler  gd32f4xx_it.o
    0x08002e74   0x08002e74   0x0000004c   Code   RO          421    i.USART1_IRQHandler  main.o
    0x08002ec0   0x08002ec0   0x00000004   Code   RO           13    i.UsageFault_Handler  gd32f4xx_it.o
    0x08002ec4   0x08002ec4   0x0000006c   Code   RO          296    i.Usart0DmaInit     gd32f470vet6_bsp.o
    0x08002f30   0x08002f30   0x000000b4   Code   RO          297    i.Usart0PeriphInit  gd32f470vet6_bsp.o
    0x08002fe4   0x08002fe4   0x00000070   Code   RO          298    i.Usart0Printf      gd32f470vet6_bsp.o
    0x08003054   0x08003054   0x00000038   Code   RO          695    i.Usart0Task        usart_app.o
    0x0800308c   0x0800308c   0x00000030   Code   RO         7460    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080030bc   0x080030bc   0x0000000e   Code   RO         7332    i._is_digit         c_w.l(__printf_wp.o)
    0x080030ca   0x080030ca   0x0000001c   Code   RO         1709    i._soft_delay_      system_gd32f4xx.o
    0x080030e6   0x080030e6   0x0000002a   Code   RO         1767    i.adc_calibration_enable  gd32f4xx_adc.o
    0x08003110   0x08003110   0x00000052   Code   RO         1769    i.adc_channel_length_config  gd32f4xx_adc.o
    0x08003162   0x08003162   0x00000002   PAD
    0x08003164   0x08003164   0x00000024   Code   RO         1770    i.adc_clock_config  gd32f4xx_adc.o
    0x08003188   0x08003188   0x00000016   Code   RO         1771    i.adc_data_alignment_config  gd32f4xx_adc.o
    0x0800319e   0x0800319e   0x0000000a   Code   RO         1776    i.adc_dma_mode_enable  gd32f4xx_adc.o
    0x080031a8   0x080031a8   0x0000000a   Code   RO         1778    i.adc_dma_request_after_last_enable  gd32f4xx_adc.o
    0x080031b2   0x080031b2   0x00000012   Code   RO         1779    i.adc_enable        gd32f4xx_adc.o
    0x080031c4   0x080031c4   0x00000034   Code   RO         1781    i.adc_external_trigger_config  gd32f4xx_adc.o
    0x080031f8   0x080031f8   0x00000030   Code   RO         1782    i.adc_external_trigger_source_config  gd32f4xx_adc.o
    0x08003228   0x08003228   0x000000ac   Code   RO         1797    i.adc_routine_channel_config  gd32f4xx_adc.o
    0x080032d4   0x080032d4   0x00000024   Code   RO         1800    i.adc_software_trigger_enable  gd32f4xx_adc.o
    0x080032f8   0x080032f8   0x0000005a   Code   RO         1801    i.adc_special_function_config  gd32f4xx_adc.o
    0x08003352   0x08003352   0x00000002   PAD
    0x08003354   0x08003354   0x00000024   Code   RO         1806    i.adc_sync_mode_config  gd32f4xx_adc.o
    0x08003378   0x08003378   0x00000026   Code   RO          713    i.bit_array_and     ebtn.o
    0x0800339e   0x0800339e   0x0000002e   Code   RO          714    i.bit_array_assign  ebtn.o
    0x080033cc   0x080033cc   0x00000024   Code   RO          715    i.bit_array_cmp     ebtn.o
    0x080033f0   0x080033f0   0x00000016   Code   RO          716    i.bit_array_get     ebtn.o
    0x08003406   0x08003406   0x00000054   Code   RO          717    i.bit_array_num_bits_set  ebtn.o
    0x0800345a   0x0800345a   0x00000026   Code   RO          718    i.bit_array_or      ebtn.o
    0x08003480   0x08003480   0x00000030   Code   RO         1133    i.cmdsent_error_check  sdio_sdcard.o
    0x080034b0   0x080034b0   0x00000014   Code   RO          508    i.delay_1ms         systick.o
    0x080034c4   0x080034c4   0x00000018   Code   RO          509    i.delay_decrement   systick.o
    0x080034dc   0x080034dc   0x00000086   Code   RO         1352    i.disk_initialize   diskio.o
    0x08003562   0x08003562   0x00000020   Code   RO         2807    i.dma_channel_disable  gd32f4xx_dma.o
    0x08003582   0x08003582   0x00000020   Code   RO         2808    i.dma_channel_enable  gd32f4xx_dma.o
    0x080035a2   0x080035a2   0x00000026   Code   RO         2809    i.dma_channel_subperipheral_select  gd32f4xx_dma.o
    0x080035c8   0x080035c8   0x00000020   Code   RO         2810    i.dma_circulation_disable  gd32f4xx_dma.o
    0x080035e8   0x080035e8   0x00000020   Code   RO         2811    i.dma_circulation_enable  gd32f4xx_dma.o
    0x08003608   0x08003608   0x000000a6   Code   RO         2812    i.dma_deinit        gd32f4xx_dma.o
    0x080036ae   0x080036ae   0x0000003e   Code   RO         2814    i.dma_flag_clear    gd32f4xx_dma.o
    0x080036ec   0x080036ec   0x0000004c   Code   RO         2815    i.dma_flag_get      gd32f4xx_dma.o
    0x08003738   0x08003738   0x00000020   Code   RO         2821    i.dma_memory_address_config  gd32f4xx_dma.o
    0x08003758   0x08003758   0x00000158   Code   RO         2832    i.dma_single_data_mode_init  gd32f4xx_dma.o
    0x080038b0   0x080038b0   0x00000022   Code   RO         2833    i.dma_single_data_para_struct_init  gd32f4xx_dma.o
    0x080038d2   0x080038d2   0x00000010   Code   RO         2837    i.dma_transfer_number_config  gd32f4xx_dma.o
    0x080038e2   0x080038e2   0x00000010   Code   RO         2838    i.dma_transfer_number_get  gd32f4xx_dma.o
    0x080038f2   0x080038f2   0x00000020   Code   RO          719    i.ebtn_combo_btn_add_btn  ebtn.o
    0x08003912   0x08003912   0x00000020   Code   RO          720    i.ebtn_combo_btn_add_btn_by_idx  ebtn.o
    0x08003932   0x08003932   0x00000002   PAD
    0x08003934   0x08003934   0x00000048   Code   RO          727    i.ebtn_get_btn_index_by_key_id  ebtn.o
    0x0800397c   0x0800397c   0x00000058   Code   RO          729    i.ebtn_get_current_state  ebtn.o
    0x080039d4   0x080039d4   0x0000005c   Code   RO          731    i.ebtn_init         ebtn.o
    0x08003a30   0x08003a30   0x0000001a   Code   RO          735    i.ebtn_process      ebtn.o
    0x08003a4a   0x08003a4a   0x0000003e   Code   RO          736    i.ebtn_process_btn  ebtn.o
    0x08003a88   0x08003a88   0x0000007c   Code   RO          737    i.ebtn_process_btn_combo  ebtn.o
    0x08003b04   0x08003b04   0x000001c0   Code   RO          738    i.ebtn_process_with_curr_state  ebtn.o
    0x08003cc4   0x08003cc4   0x0000000c   Code   RO          740    i.ebtn_set_combo_suppress_threshold  ebtn.o
    0x08003cd0   0x08003cd0   0x0000000c   Code   RO          741    i.ebtn_set_config   ebtn.o
    0x08003cdc   0x08003cdc   0x00000006   Code   RO          742    i.ebtn_timer_sub    ebtn.o
    0x08003ce2   0x08003ce2   0x00000002   PAD
    0x08003ce4   0x08003ce4   0x0000002c   Code   RO         1423    i.f_mount           ff.o
    0x08003d10   0x08003d10   0x0000005e   Code   RO         4226    i.gpio_af_set       gd32f4xx_gpio.o
    0x08003d6e   0x08003d6e   0x00000004   Code   RO         4227    i.gpio_bit_reset    gd32f4xx_gpio.o
    0x08003d72   0x08003d72   0x00000004   Code   RO         4228    i.gpio_bit_set      gd32f4xx_gpio.o
    0x08003d76   0x08003d76   0x0000000a   Code   RO         4230    i.gpio_bit_write    gd32f4xx_gpio.o
    0x08003d80   0x08003d80   0x00000074   Code   RO         1136    i.gpio_config       sdio_sdcard.o
    0x08003df4   0x08003df4   0x00000010   Code   RO         4232    i.gpio_input_bit_get  gd32f4xx_gpio.o
    0x08003e04   0x08003e04   0x0000004e   Code   RO         4234    i.gpio_mode_set     gd32f4xx_gpio.o
    0x08003e52   0x08003e52   0x00000042   Code   RO         4236    i.gpio_output_options_set  gd32f4xx_gpio.o
    0x08003e94   0x08003e94   0x00000010   Code   RO         4330    i.i2c_ack_config    gd32f4xx_i2c.o
    0x08003ea4   0x08003ea4   0x000000e4   Code   RO         4334    i.i2c_clock_config  gd32f4xx_i2c.o
    0x08003f88   0x08003f88   0x00000058   Code   RO         4337    i.i2c_deinit        gd32f4xx_i2c.o
    0x08003fe0   0x08003fe0   0x00000010   Code   RO         4340    i.i2c_dma_config    gd32f4xx_i2c.o
    0x08003ff0   0x08003ff0   0x0000000a   Code   RO         4344    i.i2c_enable        gd32f4xx_i2c.o
    0x08003ffa   0x08003ffa   0x00000028   Code   RO         4345    i.i2c_flag_clear    gd32f4xx_i2c.o
    0x08004022   0x08004022   0x0000001e   Code   RO         4346    i.i2c_flag_get      gd32f4xx_i2c.o
    0x08004040   0x08004040   0x00000014   Code   RO         4351    i.i2c_master_addressing  gd32f4xx_i2c.o
    0x08004054   0x08004054   0x0000001c   Code   RO         4352    i.i2c_mode_addr_config  gd32f4xx_i2c.o
    0x08004070   0x08004070   0x0000000a   Code   RO         4365    i.i2c_start_on_bus  gd32f4xx_i2c.o
    0x0800407a   0x0800407a   0x0000000a   Code   RO         4366    i.i2c_stop_on_bus   gd32f4xx_i2c.o
    0x08004084   0x08004084   0x0000007c   Code   RO          422    i.main              main.o
    0x08004100   0x08004100   0x000000c4   Code   RO         4799    i.nvic_irq_enable   gd32f4xx_misc.o
    0x080041c4   0x080041c4   0x00000014   Code   RO         4800    i.nvic_priority_group_set  gd32f4xx_misc.o
    0x080041d8   0x080041d8   0x00000014   Code   RO         4856    i.pmu_backup_write_enable  gd32f4xx_pmu.o
    0x080041ec   0x080041ec   0x0000004c   Code   RO          743    i.prv_get_combo_btn_by_key_id  ebtn.o
    0x08004238   0x08004238   0x00000374   Code   RO          744    i.prv_process_btn   ebtn.o
    0x080045ac   0x080045ac   0x00000084   Code   RO         1137    i.r1_error_check    sdio_sdcard.o
    0x08004630   0x08004630   0x000000ae   Code   RO         1138    i.r1_error_type_check  sdio_sdcard.o
    0x080046de   0x080046de   0x00000002   PAD
    0x080046e0   0x080046e0   0x00000050   Code   RO         1139    i.r2_error_check    sdio_sdcard.o
    0x08004730   0x08004730   0x0000003c   Code   RO         1140    i.r3_error_check    sdio_sdcard.o
    0x0800476c   0x0800476c   0x000000a8   Code   RO         1141    i.r6_error_check    sdio_sdcard.o
    0x08004814   0x08004814   0x00000050   Code   RO         1142    i.r7_error_check    sdio_sdcard.o
    0x08004864   0x08004864   0x00000014   Code   RO         4996    i.rcu_all_reset_flag_clear  gd32f4xx_rcu.o
    0x08004878   0x08004878   0x00000124   Code   RO         5004    i.rcu_clock_freq_get  gd32f4xx_rcu.o
    0x0800499c   0x0800499c   0x00000024   Code   RO         1143    i.rcu_config        sdio_sdcard.o
    0x080049c0   0x080049c0   0x00000024   Code   RO         5007    i.rcu_flag_get      gd32f4xx_rcu.o
    0x080049e4   0x080049e4   0x00000024   Code   RO         5020    i.rcu_osci_on       gd32f4xx_rcu.o
    0x08004a08   0x08004a08   0x0000015c   Code   RO         5021    i.rcu_osci_stab_wait  gd32f4xx_rcu.o
    0x08004b64   0x08004b64   0x00000024   Code   RO         5023    i.rcu_periph_clock_enable  gd32f4xx_rcu.o
    0x08004b88   0x08004b88   0x00000024   Code   RO         5026    i.rcu_periph_reset_disable  gd32f4xx_rcu.o
    0x08004bac   0x08004bac   0x00000024   Code   RO         5027    i.rcu_periph_reset_enable  gd32f4xx_rcu.o
    0x08004bd0   0x08004bd0   0x00000018   Code   RO         5032    i.rcu_rtc_clock_config  gd32f4xx_rcu.o
    0x08004be8   0x08004be8   0x0000000a   Code   RO          423    i.rs485_receive_enable  main.o
    0x08004bf2   0x08004bf2   0x00000002   PAD
    0x08004bf4   0x08004bf4   0x00000018   Code   RO          424    i.rs485_send_enable  main.o
    0x08004c0c   0x08004c0c   0x00000064   Code   RO         5304    i.rtc_current_time_get  gd32f4xx_rtc.o
    0x08004c70   0x08004c70   0x000000c4   Code   RO         5309    i.rtc_init          gd32f4xx_rtc.o
    0x08004d34   0x08004d34   0x00000048   Code   RO         5310    i.rtc_init_mode_enter  gd32f4xx_rtc.o
    0x08004d7c   0x08004d7c   0x00000014   Code   RO         5311    i.rtc_init_mode_exit  gd32f4xx_rtc.o
    0x08004d90   0x08004d90   0x00000060   Code   RO         5316    i.rtc_register_sync_wait  gd32f4xx_rtc.o
    0x08004df0   0x08004df0   0x00000094   Code   RO         1146    i.sd_bus_mode_config  sdio_sdcard.o
    0x08004e84   0x08004e84   0x000000fc   Code   RO         1147    i.sd_bus_width_config  sdio_sdcard.o
    0x08004f80   0x08004f80   0x000002c0   Code   RO         1149    i.sd_card_information_get  sdio_sdcard.o
    0x08005240   0x08005240   0x0000011c   Code   RO         1150    i.sd_card_init      sdio_sdcard.o
    0x0800535c   0x0800535c   0x00000026   Code   RO         1151    i.sd_card_select_deselect  sdio_sdcard.o
    0x08005382   0x08005382   0x00000002   PAD
    0x08005384   0x08005384   0x00000048   Code   RO         1153    i.sd_cardstatus_get  sdio_sdcard.o
    0x080053cc   0x080053cc   0x00000046   Code   RO         1156    i.sd_init           sdio_sdcard.o
    0x08005412   0x08005412   0x00000002   PAD
    0x08005414   0x08005414   0x00000130   Code   RO         1157    i.sd_interrupts_process  sdio_sdcard.o
    0x08005544   0x08005544   0x0000012c   Code   RO         1162    i.sd_power_on       sdio_sdcard.o
    0x08005670   0x08005670   0x0000015c   Code   RO         1163    i.sd_scr_get        sdio_sdcard.o
    0x080057cc   0x080057cc   0x00000018   Code   RO         1165    i.sd_transfer_mode_config  sdio_sdcard.o
    0x080057e4   0x080057e4   0x00000024   Code   RO         1167    i.sd_transfer_stop  sdio_sdcard.o
    0x08005808   0x08005808   0x0000001c   Code   RO         5557    i.sdio_bus_mode_set  gd32f4xx_sdio.o
    0x08005824   0x08005824   0x00000034   Code   RO         5564    i.sdio_clock_config  gd32f4xx_sdio.o
    0x08005858   0x08005858   0x00000014   Code   RO         5566    i.sdio_clock_enable  gd32f4xx_sdio.o
    0x0800586c   0x0800586c   0x0000000c   Code   RO         5567    i.sdio_command_index_get  gd32f4xx_sdio.o
    0x08005878   0x08005878   0x00000038   Code   RO         5568    i.sdio_command_response_config  gd32f4xx_sdio.o
    0x080058b0   0x080058b0   0x00000014   Code   RO         5570    i.sdio_csm_enable   gd32f4xx_sdio.o
    0x080058c4   0x080058c4   0x0000003c   Code   RO         5571    i.sdio_data_config  gd32f4xx_sdio.o
    0x08005900   0x08005900   0x0000000c   Code   RO         5573    i.sdio_data_read    gd32f4xx_sdio.o
    0x0800590c   0x0800590c   0x0000001c   Code   RO         5574    i.sdio_data_transfer_config  gd32f4xx_sdio.o
    0x08005928   0x08005928   0x00000014   Code   RO         5576    i.sdio_deinit       gd32f4xx_sdio.o
    0x0800593c   0x0800593c   0x00000014   Code   RO         5580    i.sdio_dsm_enable   gd32f4xx_sdio.o
    0x08005950   0x08005950   0x0000000c   Code   RO         5582    i.sdio_flag_clear   gd32f4xx_sdio.o
    0x0800595c   0x0800595c   0x00000014   Code   RO         5583    i.sdio_flag_get     gd32f4xx_sdio.o
    0x08005970   0x08005970   0x00000014   Code   RO         5584    i.sdio_hardware_clock_disable  gd32f4xx_sdio.o
    0x08005984   0x08005984   0x00000010   Code   RO         5586    i.sdio_interrupt_disable  gd32f4xx_sdio.o
    0x08005994   0x08005994   0x0000000c   Code   RO         5588    i.sdio_interrupt_flag_clear  gd32f4xx_sdio.o
    0x080059a0   0x080059a0   0x00000014   Code   RO         5589    i.sdio_interrupt_flag_get  gd32f4xx_sdio.o
    0x080059b4   0x080059b4   0x0000000c   Code   RO         5592    i.sdio_power_state_get  gd32f4xx_sdio.o
    0x080059c0   0x080059c0   0x0000000c   Code   RO         5593    i.sdio_power_state_set  gd32f4xx_sdio.o
    0x080059cc   0x080059cc   0x0000003c   Code   RO         5597    i.sdio_response_get  gd32f4xx_sdio.o
    0x08005a08   0x08005a08   0x0000001c   Code   RO         5602    i.sdio_wait_type_set  gd32f4xx_sdio.o
    0x08005a24   0x08005a24   0x0000000a   Code   RO         5863    i.spi_enable        gd32f4xx_spi.o
    0x08005a2e   0x08005a2e   0x00000002   PAD
    0x08005a30   0x08005a30   0x0000001c   Code   RO         1037    i.spi_flash_init    gd25qxx.o
    0x08005a4c   0x08005a4c   0x00000032   Code   RO         5873    i.spi_init          gd32f4xx_spi.o
    0x08005a7e   0x08005a7e   0x00000002   PAD
    0x08005a80   0x08005a80   0x00000110   Code   RO         1711    i.system_clock_240m_25m_hxtal  system_gd32f4xx.o
    0x08005b90   0x08005b90   0x00000008   Code   RO         1712    i.system_clock_config  system_gd32f4xx.o
    0x08005b98   0x08005b98   0x00000050   Code   RO          510    i.systick_config    systick.o
    0x08005be8   0x08005be8   0x0000001c   Code   RO          425    i.usart1_enable_receive_interrupt  main.o
    0x08005c04   0x08005c04   0x000000bc   Code   RO          426    i.usart1_rs485_init  main.o
    0x08005cc0   0x08005cc0   0x00000020   Code   RO          427    i.usart1_send_byte  main.o
    0x08005ce0   0x08005ce0   0x00000034   Code   RO          428    i.usart1_send_string  main.o
    0x08005d14   0x08005d14   0x000000e8   Code   RO         6848    i.usart_baudrate_set  gd32f4xx_usart.o
    0x08005dfc   0x08005dfc   0x0000000a   Code   RO         6852    i.usart_data_receive  gd32f4xx_usart.o
    0x08005e06   0x08005e06   0x00000008   Code   RO         6853    i.usart_data_transmit  gd32f4xx_usart.o
    0x08005e0e   0x08005e0e   0x00000002   PAD
    0x08005e10   0x08005e10   0x000000dc   Code   RO         6854    i.usart_deinit      gd32f4xx_usart.o
    0x08005eec   0x08005eec   0x00000014   Code   RO         6856    i.usart_dma_receive_config  gd32f4xx_usart.o
    0x08005f00   0x08005f00   0x0000000a   Code   RO         6858    i.usart_enable      gd32f4xx_usart.o
    0x08005f0a   0x08005f0a   0x0000001e   Code   RO         6860    i.usart_flag_get    gd32f4xx_usart.o
    0x08005f28   0x08005f28   0x0000001a   Code   RO         6867    i.usart_interrupt_disable  gd32f4xx_usart.o
    0x08005f42   0x08005f42   0x0000001a   Code   RO         6868    i.usart_interrupt_enable  gd32f4xx_usart.o
    0x08005f5c   0x08005f5c   0x0000001a   Code   RO         6869    i.usart_interrupt_flag_clear  gd32f4xx_usart.o
    0x08005f76   0x08005f76   0x00000038   Code   RO         6870    i.usart_interrupt_flag_get  gd32f4xx_usart.o
    0x08005fae   0x08005fae   0x00000010   Code   RO         6883    i.usart_parity_config  gd32f4xx_usart.o
    0x08005fbe   0x08005fbe   0x00000010   Code   RO         6885    i.usart_receive_config  gd32f4xx_usart.o
    0x08005fce   0x08005fce   0x00000010   Code   RO         6896    i.usart_stop_bit_set  gd32f4xx_usart.o
    0x08005fde   0x08005fde   0x00000010   Code   RO         6900    i.usart_transmit_config  gd32f4xx_usart.o
    0x08005fee   0x08005fee   0x00000010   Code   RO         6901    i.usart_word_length_set  gd32f4xx_usart.o
    0x08005ffe   0x08005ffe   0x00000002   PAD
    0x08006000   0x08006000   0x0000002c   Code   RO         7448    locale$$code        c_w.l(lc_numeric_c.o)
    0x0800602c   0x0800602c   0x0000002c   Code   RO         7468    locale$$code        c_w.l(lc_ctype_c.o)
    0x08006058   0x08006058   0x0000000a   Code   RO         7524    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08006062   0x08006062   0x00000004   Code   RO         7369    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08006066   0x08006066   0x00000004   Code   RO         7371    x$fpl$printf2       fz_wm.l(printf2.o)
    0x0800606a   0x0800606a   0x00000000   Code   RO         7377    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800606a   0x0800606a   0x0000000e   Data   RO          300    .constdata          gd32f470vet6_bsp.o
    0x08006078   0x08006078   0x00000008   Data   RO         7291    .constdata          c_w.l(_printf_wctomb.o)
    0x08006080   0x08006080   0x00000028   Data   RO         7320    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x080060a8   0x080060a8   0x00000011   Data   RO         7340    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x080060b9   0x080060b9   0x00000026   Data   RO         7400    .constdata          c_w.l(_printf_fp_hex.o)
    0x080060df   0x080060df   0x00000001   PAD
    0x080060e0   0x080060e0   0x00000094   Data   RO         7423    .constdata          c_w.l(bigflt0.o)
    0x08006174   0x08006174   0x00000020   Data   RO         7594    Region$$Table       anon$$obj.o
    0x08006194   0x08006194   0x0000001c   Data   RO         7447    locale$$data        c_w.l(lc_numeric_c.o)
    0x080061b0   0x080061b0   0x00000110   Data   RO         7467    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080062c0, Size: 0x000015f8, Max: 0x00030000, ABSOLUTE, COMPRESSED[0x00000070])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000151   Data   RW          301    .data               gd32f470vet6_bsp.o
    0x20000151   COMPRESSED   0x00000001   PAD
    0x20000152   COMPRESSED   0x00000002   Data   RW          430    .data               main.o
    0x20000154   COMPRESSED   0x00000004   Data   RW          511    .data               systick.o
    0x20000158   COMPRESSED   0x00000016   Data   RW          930    .data               oled.o
    0x2000016e   COMPRESSED   0x00000002   PAD
    0x20000170   COMPRESSED   0x00000024   Data   RW         1169    .data               sdio_sdcard.o
    0x20000194   COMPRESSED   0x00000006   Data   RW         1454    .data               ff.o
    0x2000019a   COMPRESSED   0x00000002   PAD
    0x2000019c   COMPRESSED   0x00000004   Data   RW         1713    .data               system_gd32f4xx.o
    0x200001a0        -       0x00000a9c   Zero   RW          299    .bss                gd32f470vet6_bsp.o
    0x20000c3c        -       0x00000100   Zero   RW          429    .bss                main.o
    0x20000d3c        -       0x0000003c   Zero   RW          745    .bss                ebtn.o
    0x20000d78        -       0x00000020   Zero   RW         1168    .bss                sdio_sdcard.o
    0x20000d98        -       0x00000060   Zero   RW         7463    .bss                c_w.l(libspace.o)
    0x20000df8        -       0x00000400   Zero   RW         1759    HEAP                startup_gd32f450_470.o
    0x200011f8        -       0x00000400   Zero   RW         1758    STACK               startup_gd32f450_470.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        84         22          0          0          0        630   adc_app.o
       224          8          0          0          0       1841   btn_app.o
       134          0          0          0          0        934   diskio.o
      2230         42          0          0         60      25964   ebtn.o
        44          6          0          6          0       6394   ff.o
        28          8          0          0          0        609   gd25qxx.o
      2416        416         14        337       2716      18356   gd32f470vet6_bsp.o
       654         16          0          0          0      10078   gd32f4xx_adc.o
       912          4          0          0          0      11056   gd32f4xx_dma.o
       272          0          0          0          0       5774   gd32f4xx_gpio.o
       496         16          0          0          0       8647   gd32f4xx_i2c.o
       228         32          0          0          0     107673   gd32f4xx_it.o
       216         20          0          0          0       1700   gd32f4xx_misc.o
        20          6          0          0          0        654   gd32f4xx_pmu.o
       864         66          0          0          0       8088   gd32f4xx_rcu.o
       484         26          0          0          0       4914   gd32f4xx_rtc.o
       540        108          0          0          0      15128   gd32f4xx_sdio.o
        60          0          0          0          0       2248   gd32f4xx_spi.o
       744         18          0          0          0      12511   gd32f4xx_usart.o
        20          4          0          0          0        570   led_app.o
       534        100          0          2        256       5925   main.o
      1028         48          0         22          0       6191   oled.o
       112         60          0          0          0        574   rtc_app.o
        92         14          0          0          0        651   scheduler.o
      3474        154          0         36         32      24754   sdio_sdcard.o
        64         26        428          0       2048        944   startup_gd32f450_470.o
       688         30          0          4          0       4647   system_gd32f4xx.o
       164         24          0          4          0      29806   systick.o
        56         18          0          0          0        593   usart_app.o

    ----------------------------------------------------------------------
     16910       <USER>        <GROUP>        416       5112     317854   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        28          0          0          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        88          0          0          0          0         76   memcmp.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
        48          0          0          0          0        124   fpclassify.o

    ----------------------------------------------------------------------
      7344        <USER>        <GROUP>          0         96       4808   Library Totals
        16          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7262        266        551          0         96       4336   c_w.l
        18          0          0          0          0        348   fz_wm.l
        48          0          0          0          0        124   m_wm.l

    ----------------------------------------------------------------------
      7344        <USER>        <GROUP>          0         96       4808   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     24254       1558       1026        416       5208     300270   Grand Totals
     24254       1558       1026        112       5208     300270   ELF Image Totals (compressed)
     24254       1558       1026        112          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                25280 (  24.69kB)
    Total RW  Size (RW Data + ZI Data)              5624 (   5.49kB)
    Total ROM Size (Code + RO Data + RW Data)      25392 (  24.80kB)

==============================================================================

