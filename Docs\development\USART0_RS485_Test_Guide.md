# USART0 RS485测试指南

## 🎯 测试目的
由于USART1的AF配置问题无法解决，现在使用USART0 (PA9/PA10) 来验证RS485硬件连接是否正常工作。

## 🔧 硬件连接变更

### 原连接 (USART1 - 不工作)
```
GD32F470VET6    →    MAX3485ESA
PA1 (方向控制)   →    DE和RE引脚
PA2 (USART1_TX) →    DI引脚 (数据输入) ❌ 不工作
PA3 (USART1_RX) →    RO引脚 (数据输出)
```

### 新连接 (USART0 - 应该工作)
```
GD32F470VET6    →    MAX3485ESA
PA1 (方向控制)   →    DE和RE引脚 (保持不变)
PA9 (USART0_TX) →    DI引脚 (数据输入) ✅ 新连接
PA10(USART0_RX) →    RO引脚 (数据输出) ✅ 新连接
```

## 🚨 重要：需要重新连线

**您需要进行以下硬件连接变更：**

1. **断开PA2连接**：将原来连接到MAX3485ESA DI引脚的PA2线断开
2. **连接PA9到DI**：将PA9连接到MAX3485ESA的DI引脚
3. **断开PA3连接**：将原来连接到MAX3485ESA RO引脚的PA3线断开  
4. **连接PA10到RO**：将PA10连接到MAX3485ESA的RO引脚
5. **PA1保持不变**：PA1仍然连接到DE/RE引脚

## 📋 测试流程

### 1. 硬件连接
按照上述连接图重新连接硬件

### 2. 编译烧录
编译并烧录当前代码到GD32F470VET6

### 3. 观察现象
- **LED0**：每2秒闪烁一次，表示正在发送数据
- **RS485接收端**：应该能接收到 "USART0 RS485 Test - Hello World!" 消息

### 4. 预期结果
- ✅ **成功**：RS485接收端能正常接收数据 → RS485硬件连接正常，问题确实在USART1配置
- ❌ **失败**：仍然无法接收数据 → 可能是MAX3485ESA硬件问题或其他连接问题

## 🔍 故障排除

### 如果USART0测试成功
说明：
- MAX3485ESA芯片正常
- PA1方向控制正常
- RS485差分信号线连接正常
- **问题确实在USART1的配置上**

**解决方案**：
- 继续使用USART0进行RS485通信
- 或者深入研究USART1的正确配置

### 如果USART0测试仍然失败
可能原因：
- MAX3485ESA芯片损坏
- 电源连接问题
- A、B差分信号线连接错误
- 接收端设备问题

**排查步骤**：
1. 检查MAX3485ESA的3.3V电源
2. 用万用表测试A、B线的连通性
3. 更换MAX3485ESA芯片
4. 检查接收端设备配置

## 📊 测试数据记录

| 测试项目 | 结果 | 备注 |
|----------|------|------|
| LED0闪烁 |      | 每2秒一次 |
| PA1电压变化 | ✅ | 已验证正常 |
| USART0发送 |      | 待测试 |
| RS485接收 |      | 待测试 |

## 🚀 下一步计划

### 如果USART0测试成功
1. 将RS485通信固定使用USART0
2. 更新所有相关代码和文档
3. 项目可以正常使用

### 如果需要使用USART1
1. 查阅GD32F470VET6完整数据手册
2. 确认USART1的正确引脚映射
3. 可能需要使用其他引脚组合

---
**测试时间**：
**测试结果**：
**下一步行动**：