# USART1 AF配置测试结果记录

## 测试目的
确定GD32F470VET6芯片上USART1在PA2/PA3引脚的正确AF配置

## 测试方法
程序将依次尝试AF0到AF15的配置，每个配置发送特定的测试消息：
- AF0: "AF0:Hello RS485!"
- AF1: "AF1:Hello RS485!"
- ...
- AF15: "AF15:Hello RS485!"

## 测试设备要求
- RS485接收设备（如另一个RS485模块、USB转RS485适配器等）
- 串口调试助手或示波器

## 测试结果记录表

| AF配置 | 发送状态 | 接收到的数据 | 备注 |
|--------|----------|--------------|------|
| AF0    |          |              |      |
| AF1    |          |              |      |
| AF2    |          |              |      |
| AF3    |          |              |      |
| AF4    |          |              |      |
| AF5    |          |              |      |
| AF6    |          |              |      |
| AF7    |          |              |      |
| AF8    |          |              |      |
| AF9    |          |              |      |
| AF10   |          |              |      |
| AF11   |          |              |      |
| AF12   |          |              |      |
| AF13   |          |              |      |
| AF14   |          |              |      |
| AF15   |          |              |      |

## 测试步骤
1. 编译并烧录修改后的代码
2. 连接RS485接收设备
3. 观察接收设备，记录收到的数据
4. 每2秒会切换到下一个AF配置
5. 总测试时间约32秒（16个配置 × 2秒）

## 预期结果
- 在某个特定的AF配置下，应该能接收到完整的测试消息
- 该AF配置就是USART1的正确配置

## 常见AF配置参考
根据常见的STM32/GD32芯片经验：
- USART1通常使用AF7或AF4
- 但GD32F470可能有不同的映射

## 测试完成后的操作
1. 记录工作的AF配置号
2. 在代码中固定使用该AF配置
3. 移除测试代码，恢复正常的RS485通信功能

---
**测试开始时间**：
**测试完成时间**：
**工作的AF配置**：
**测试人员**：